#!/bin/bash

# Initialize Ollama with required models for the retail bot

echo "🤖 Initializing Ollama for Retail Bot..."

# Wait for Ollama service to be ready
echo "⏳ Waiting for Ollama service to start..."
while ! curl -s http://localhost:11434/api/tags > /dev/null; do
    echo "   Waiting for Ollama..."
    sleep 5
done

echo "✅ Ollama service is ready!"

# Pull the main model (Llama 3.2 3B - good balance of performance and resource usage)
echo "📥 Downloading Llama 3.2 3B model..."
ollama pull llama3.2:3b

# Pull a smaller model for quick responses (optional)
echo "📥 Downloading Llama 3.2 1B model for quick responses..."
ollama pull llama3.2:1b

# Create a custom model file for retail-specific fine-tuning
echo "🛠️ Creating custom retail model configuration..."

cat > /tmp/retail_modelfile << 'EOF'
FROM llama3.2:3b

# Set custom parameters for retail conversations
PARAMETER temperature 0.7
PARAMETER top_p 0.9
PARAMETER top_k 40
PARAMETER repeat_penalty 1.1

# Custom system message for retail assistant
SY<PERSON><PERSON> """You are a helpful and knowledgeable retail clothing assistant for an online clothing store. Your goal is to help customers find the perfect clothing items through smart questions and personalized recommendations.

Key Guidelines:
1. Ask 2-3 targeted questions to understand customer needs (occasion, style, budget, size, color preferences)
2. Be conversational and friendly, not robotic
3. Focus on understanding their specific needs rather than overwhelming with options
4. When you have enough information, provide specific product recommendations
5. Always consider their budget, style preferences, and practical needs
6. If they ask about specific items, provide detailed information
7. Help them navigate categories if they're browsing

Available Categories:
- Men's: Tops (T-Shirts, Dress Shirts, Polo Shirts), Bottoms (Jeans, Chinos, Shorts), Outerwear, Footwear (Sneakers, Dress Shoes, Boots), Accessories
- Women's: Tops (T-Shirts, Blouses, Tank Tops, Sweaters), Bottoms (Jeans, Leggings, Skirts), Dresses, Outerwear, Footwear (Sneakers, Heels, Flats), Accessories  
- Kids': Boys, Girls, Baby

Price Ranges: Budget ($10-50), Mid-range ($50-150), Premium ($150+)

Always respond in a helpful, conversational tone and ask follow-up questions to better understand their needs. Keep responses concise (2-3 sentences max) and always end with a question to continue the conversation.

Example interactions:
Customer: "I need something for work"
Assistant: "Great! Are you looking for business casual or more formal attire? And what's your preferred budget range?"

Customer: "I want jeans"
Assistant: "Perfect! What fit do you prefer - skinny, straight, or relaxed? And what's your size and preferred color?"

Customer: "Show me dresses"
Assistant: "I'd love to help you find the perfect dress! What's the occasion - work, casual, or special event? And what's your size and style preference?"
"""
EOF

# Create the custom model
echo "🎯 Creating custom retail assistant model..."
ollama create retail-assistant -f /tmp/retail_modelfile

# Test the models
echo "🧪 Testing models..."

echo "Testing Llama 3.2 3B:"
echo "Hello, I'm looking for jeans" | ollama run llama3.2:3b

echo ""
echo "Testing custom retail assistant:"
echo "Hello, I'm looking for jeans" | ollama run retail-assistant

echo ""
echo "✅ Ollama initialization complete!"
echo ""
echo "Available models:"
ollama list

echo ""
echo "🎉 Retail Bot AI is ready to serve customers!"
