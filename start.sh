#!/bin/bash

# Retail Clothing Bot Startup Script

set -e

echo "🚀 Starting Retail Clothing Bot with Local AI..."
echo "================================================"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created - Local AI will be used (no external API keys required)"
    echo ""
fi

echo "🤖 Using Local AI (Ollama) - No external API keys required!"
echo "   - Secure: All data stays on your machine"
echo "   - Private: No data sent to external services"
echo "   - Learning: AI improves based on your interactions"
echo ""

echo "🐳 Starting Docker services..."
docker-compose down --remove-orphans
docker-compose up -d

echo "⏳ Waiting for services to start..."
sleep 10

# Wait for database to be ready
echo "🗄️  Waiting for database..."
for i in {1..30}; do
    if docker-compose exec -T postgres pg_isready -U retail_user -d retail_bot_db > /dev/null 2>&1; then
        echo "✅ Database is ready"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ Database failed to start"
        docker-compose logs postgres
        exit 1
    fi
    sleep 2
done

# Wait for Ollama to be ready
echo "🤖 Waiting for Ollama AI service..."
for i in {1..60}; do
    if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
        echo "✅ Ollama is ready"
        break
    fi
    if [ $i -eq 60 ]; then
        echo "❌ Ollama failed to start"
        docker-compose logs ollama
        exit 1
    fi
    sleep 5
done

# Initialize Ollama models
echo "📥 Initializing AI models (this may take several minutes on first run)..."
chmod +x scripts/init_ollama.sh
if ./scripts/init_ollama.sh; then
    echo "✅ AI models initialized successfully"
else
    echo "⚠️  AI model initialization had issues, but continuing..."
fi

# Wait for backend to be ready
echo "🔧 Waiting for backend..."
for i in {1..30}; do
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ Backend is ready"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ Backend failed to start"
        docker-compose logs backend
        exit 1
    fi
    sleep 2
done

# Wait for frontend to be ready
echo "🎨 Waiting for frontend..."
for i in {1..30}; do
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        echo "✅ Frontend is ready"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ Frontend failed to start"
        docker-compose logs frontend
        exit 1
    fi
    sleep 2
done

echo ""
echo "🎉 Retail Clothing Bot is now running!"
echo "=================================="
echo ""
echo "📱 Access your application:"
echo "   Frontend:    http://localhost:3000"
echo "   API Docs:    http://localhost:8000/docs"
echo "   Health:      http://localhost:8000/health"
echo ""
echo "🔧 Useful commands:"
echo "   View logs:   docker-compose logs -f"
echo "   Stop:        docker-compose down"
echo "   Restart:     docker-compose restart"
echo ""

# Run tests if Python is available
if command -v python3 > /dev/null 2>&1; then
    echo "🧪 Running setup tests..."
    python3 test_setup.py
else
    echo "⚠️  Python3 not found. Skipping automated tests."
    echo "   You can manually test by visiting http://localhost:3000"
fi

echo ""
echo "🎯 Ready to start shopping! Visit http://localhost:3000 and click 'Start Shopping Chat'"
