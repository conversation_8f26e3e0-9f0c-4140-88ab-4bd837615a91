#!/bin/bash

# Retail Clothing Bot Startup Script

set -e

echo "🚀 Starting Retail Clothing Bot..."
echo "=================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file and add your OPENAI_API_KEY"
    echo "   You can get an API key from: https://platform.openai.com/api-keys"
    echo ""
    echo "   After adding your API key, run this script again."
    exit 1
fi

# Check if OpenAI API key is set
if ! grep -q "OPENAI_API_KEY=sk-" .env 2>/dev/null; then
    echo "⚠️  OpenAI API key not found in .env file"
    echo "   The bot will work with limited functionality (no AI features)"
    echo "   To enable AI features, add your OpenAI API key to .env file"
    echo ""
    read -p "Continue without AI features? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Please add your OpenAI API key to .env file and try again."
        exit 1
    fi
fi

echo "🐳 Starting Docker services..."
docker-compose down --remove-orphans
docker-compose up -d

echo "⏳ Waiting for services to start..."
sleep 10

# Wait for database to be ready
echo "🗄️  Waiting for database..."
for i in {1..30}; do
    if docker-compose exec -T postgres pg_isready -U retail_user -d retail_bot_db > /dev/null 2>&1; then
        echo "✅ Database is ready"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ Database failed to start"
        docker-compose logs postgres
        exit 1
    fi
    sleep 2
done

# Wait for backend to be ready
echo "🔧 Waiting for backend..."
for i in {1..30}; do
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ Backend is ready"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ Backend failed to start"
        docker-compose logs backend
        exit 1
    fi
    sleep 2
done

# Wait for frontend to be ready
echo "🎨 Waiting for frontend..."
for i in {1..30}; do
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        echo "✅ Frontend is ready"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ Frontend failed to start"
        docker-compose logs frontend
        exit 1
    fi
    sleep 2
done

echo ""
echo "🎉 Retail Clothing Bot is now running!"
echo "=================================="
echo ""
echo "📱 Access your application:"
echo "   Frontend:    http://localhost:3000"
echo "   API Docs:    http://localhost:8000/docs"
echo "   Health:      http://localhost:8000/health"
echo ""
echo "🔧 Useful commands:"
echo "   View logs:   docker-compose logs -f"
echo "   Stop:        docker-compose down"
echo "   Restart:     docker-compose restart"
echo ""

# Run tests if Python is available
if command -v python3 > /dev/null 2>&1; then
    echo "🧪 Running setup tests..."
    python3 test_setup.py
else
    echo "⚠️  Python3 not found. Skipping automated tests."
    echo "   You can manually test by visiting http://localhost:3000"
fi

echo ""
echo "🎯 Ready to start shopping! Visit http://localhost:3000 and click 'Start Shopping Chat'"
