#!/usr/bin/env python3
"""
Test script to verify the retail clothing bot setup
"""

import requests
import json
import time
import sys

def test_backend_health():
    """Test backend health endpoint"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=10)
        if response.status_code == 200:
            print("✅ Backend health check passed")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend health check failed: {e}")
        return False

def test_database_connection():
    """Test database by fetching categories"""
    try:
        response = requests.get("http://localhost:8000/api/v1/categories/l1", timeout=10)
        if response.status_code == 200:
            categories = response.json()
            if len(categories) > 0:
                print(f"✅ Database connection successful - Found {len(categories)} L1 categories")
                return True
            else:
                print("❌ Database connection failed - No categories found")
                return False
        else:
            print(f"❌ Database connection failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_products_endpoint():
    """Test products endpoint"""
    try:
        response = requests.get("http://localhost:8000/api/v1/products/?limit=5", timeout=10)
        if response.status_code == 200:
            products = response.json()
            if len(products) > 0:
                print(f"✅ Products endpoint working - Found {len(products)} products")
                return True
            else:
                print("❌ Products endpoint failed - No products found")
                return False
        else:
            print(f"❌ Products endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Products endpoint failed: {e}")
        return False

def test_search_endpoint():
    """Test search endpoint"""
    try:
        response = requests.get("http://localhost:8000/api/v1/search/?q=jeans&page_size=3", timeout=10)
        if response.status_code == 200:
            search_result = response.json()
            if search_result.get('total_count', 0) > 0:
                print(f"✅ Search endpoint working - Found {search_result['total_count']} results for 'jeans'")
                return True
            else:
                print("❌ Search endpoint failed - No search results")
                return False
        else:
            print(f"❌ Search endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Search endpoint failed: {e}")
        return False

def test_chat_endpoint():
    """Test chat endpoint (basic functionality without OpenAI)"""
    try:
        chat_data = {
            "message": "Hello, I'm looking for jeans"
        }
        response = requests.post(
            "http://localhost:8000/api/v1/chat/", 
            json=chat_data, 
            timeout=15
        )
        if response.status_code == 200:
            chat_response = response.json()
            if chat_response.get('message') and chat_response.get('session_token'):
                print("✅ Chat endpoint working - Bot responded successfully")
                return True
            else:
                print("❌ Chat endpoint failed - Invalid response format")
                return False
        else:
            print(f"❌ Chat endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Chat endpoint failed: {e}")
        return False

def test_frontend():
    """Test frontend availability"""
    try:
        response = requests.get("http://localhost:3000", timeout=10)
        if response.status_code == 200:
            print("✅ Frontend is accessible")
            return True
        else:
            print(f"❌ Frontend failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Frontend failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Retail Clothing Bot Setup")
    print("=" * 50)
    
    tests = [
        ("Backend Health", test_backend_health),
        ("Database Connection", test_database_connection),
        ("Products Endpoint", test_products_endpoint),
        ("Search Endpoint", test_search_endpoint),
        ("Chat Endpoint", test_chat_endpoint),
        ("Frontend", test_frontend),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Testing {test_name}...")
        if test_func():
            passed += 1
        time.sleep(1)  # Brief pause between tests
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your retail clothing bot is ready to use.")
        print("\n📱 Access your bot at:")
        print("   Frontend: http://localhost:3000")
        print("   API Docs: http://localhost:8000/docs")
        return True
    else:
        print("⚠️  Some tests failed. Please check the logs and configuration.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
