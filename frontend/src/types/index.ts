export interface Product {
  id: string
  name: string
  slug: string
  description?: string
  short_description?: string
  base_price: number
  sale_price?: number
  currency: string
  rating_average: number
  rating_count: number
  primary_image?: string
  brand_name?: string
  category_path?: string
  images?: ProductImage[]
  variants?: ProductVariant[]
  brand?: Brand
  category_l1?: Category
  category_l2?: Category
  category_l3?: Category
}

export interface ProductImage {
  id: string
  product_id: string
  image_url: string
  alt_text?: string
  is_primary: boolean
  sort_order: number
}

export interface ProductVariant {
  id: string
  product_id: string
  variant_type: string
  variant_value: string
  price_adjustment: number
  stock_quantity: number
  is_active: boolean
}

export interface Brand {
  id: string
  name: string
  slug: string
  description?: string
  logo_url?: string
}

export interface Category {
  id: string
  name: string
  slug: string
  level: number
  parent_id?: string
  description?: string
  image_url?: string
  children?: Category[]
}

export interface ChatMessage {
  role: 'user' | 'assistant'
  content: string
  timestamp: string
}

export interface ChatRequest {
  message: string
  session_token?: string
}

export interface ChatResponse {
  message: string
  session_token: string
  suggested_products?: Product[]
  suggested_categories?: Category[]
  context?: Record<string, any>
}

export interface CustomerPreferences {
  gender?: string
  size_preferences?: Record<string, string>
  color_preferences?: string[]
  brand_preferences?: string[]
  price_range?: {
    min: number
    max: number
  }
  style_preferences?: string[]
}

export interface SearchFilters {
  category_l1?: string
  category_l2?: string
  category_l3?: string
  brand?: string
  min_price?: number
  max_price?: number
  size?: string
  color?: string
  tags?: string[]
  in_stock?: boolean
}

export interface SearchRequest {
  query?: string
  filters?: SearchFilters
  page?: number
  page_size?: number
  sort_by?: string
}

export interface SearchResponse {
  products: Product[]
  total_count: number
  page: number
  page_size: number
  total_pages: number
  filters_applied?: SearchFilters
}
