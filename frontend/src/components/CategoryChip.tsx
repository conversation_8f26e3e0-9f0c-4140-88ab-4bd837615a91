import React from 'react'
import { Category } from '../types'

interface CategoryChipProps {
  category: Category
  onClick?: () => void
  active?: boolean
}

const CategoryChip: React.FC<CategoryChipProps> = ({ category, onClick, active = false }) => {
  return (
    <button
      onClick={onClick}
      className={`category-chip ${
        active 
          ? 'bg-primary-100 text-primary-800 border-primary-200' 
          : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
      }`}
    >
      {category.name}
    </button>
  )
}

export default CategoryChip
