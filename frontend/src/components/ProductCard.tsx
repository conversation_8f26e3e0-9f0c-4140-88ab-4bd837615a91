import React from 'react'
import { Star, ShoppingCart } from 'lucide-react'
import { Product } from '../types'

interface ProductCardProps {
  product: Product
  compact?: boolean
  onClick?: () => void
}

const ProductCard: React.FC<ProductCardProps> = ({ product, compact = false, onClick }) => {
  const displayPrice = product.sale_price || product.base_price
  const hasDiscount = product.sale_price && product.sale_price < product.base_price

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: product.currency || 'USD',
    }).format(price)
  }

  const renderStars = (rating: number) => {
    const stars = []
    const fullStars = Math.floor(rating)
    const hasHalfStar = rating % 1 !== 0

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Star key={i} className="w-3 h-3 fill-yellow-400 text-yellow-400" />
      )
    }

    if (hasHalfStar) {
      stars.push(
        <Star key="half" className="w-3 h-3 fill-yellow-400/50 text-yellow-400" />
      )
    }

    const emptyStars = 5 - Math.ceil(rating)
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <Star key={`empty-${i}`} className="w-3 h-3 text-gray-300" />
      )
    }

    return stars
  }

  if (compact) {
    return (
      <div
        onClick={onClick}
        className="product-card cursor-pointer group"
      >
        <div className="aspect-square bg-gray-100 rounded-md mb-2 overflow-hidden">
          {product.primary_image ? (
            <img
              src={product.primary_image}
              alt={product.name}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-gray-400">
              <ShoppingCart className="w-6 h-6" />
            </div>
          )}
        </div>
        
        <div className="space-y-1">
          <h3 className="text-xs font-medium text-gray-900 line-clamp-2">
            {product.name}
          </h3>
          
          <div className="flex items-center space-x-1">
            <span className="text-sm font-semibold text-gray-900">
              {formatPrice(displayPrice)}
            </span>
            {hasDiscount && (
              <span className="text-xs text-gray-500 line-through">
                {formatPrice(product.base_price)}
              </span>
            )}
          </div>
          
          {product.rating_average > 0 && (
            <div className="flex items-center space-x-1">
              <div className="flex">
                {renderStars(product.rating_average)}
              </div>
              <span className="text-xs text-gray-500">
                ({product.rating_count})
              </span>
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <div
      onClick={onClick}
      className="product-card cursor-pointer group"
    >
      <div className="aspect-square bg-gray-100 rounded-lg mb-4 overflow-hidden relative">
        {product.primary_image ? (
          <img
            src={product.primary_image}
            alt={product.name}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-gray-400">
            <ShoppingCart className="w-12 h-12" />
          </div>
        )}
        
        {hasDiscount && (
          <div className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">
            Sale
          </div>
        )}
      </div>
      
      <div className="space-y-2">
        {product.brand_name && (
          <p className="text-sm text-gray-500 font-medium">
            {product.brand_name}
          </p>
        )}
        
        <h3 className="font-semibold text-gray-900 line-clamp-2">
          {product.name}
        </h3>
        
        {product.short_description && (
          <p className="text-sm text-gray-600 line-clamp-2">
            {product.short_description}
          </p>
        )}
        
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-lg font-bold text-gray-900">
              {formatPrice(displayPrice)}
            </span>
            {hasDiscount && (
              <span className="text-sm text-gray-500 line-through">
                {formatPrice(product.base_price)}
              </span>
            )}
          </div>
          
          {product.rating_average > 0 && (
            <div className="flex items-center space-x-1">
              <div className="flex">
                {renderStars(product.rating_average)}
              </div>
              <span className="text-sm text-gray-500">
                ({product.rating_count})
              </span>
            </div>
          )}
        </div>
        
        {product.category_path && (
          <p className="text-xs text-gray-500">
            {product.category_path}
          </p>
        )}
      </div>
    </div>
  )
}

export default ProductCard
