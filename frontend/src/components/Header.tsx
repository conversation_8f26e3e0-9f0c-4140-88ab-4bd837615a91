import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { MessageCircle, ShoppingBag, Home } from 'lucide-react'
import { useChatStore } from '../store/chatStore'

const Header: React.FC = () => {
  const location = useLocation()
  const { openChat } = useChatStore()

  const isActive = (path: string) => location.pathname === path

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <ShoppingBag className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900">StyleBot</span>
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link
              to="/"
              className={`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                isActive('/')
                  ? 'text-primary-600 bg-primary-50'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <Home className="w-4 h-4" />
              <span>Home</span>
            </Link>
            
            <Link
              to="/products"
              className={`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                isActive('/products')
                  ? 'text-primary-600 bg-primary-50'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <ShoppingBag className="w-4 h-4" />
              <span>Products</span>
            </Link>
          </nav>

          {/* Chat Button */}
          <button
            onClick={() => openChat()}
            className="btn-primary flex items-center space-x-2"
          >
            <MessageCircle className="w-4 h-4" />
            <span className="hidden sm:inline">Chat Assistant</span>
          </button>
        </div>
      </div>
    </header>
  )
}

export default Header
