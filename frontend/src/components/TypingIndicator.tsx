import React from 'react'
import { motion } from 'framer-motion'

const TypingIndicator: React.FC = () => {
  return (
    <div className="flex justify-start">
      <div className="chat-message-bot flex items-center space-x-1">
        <div className="flex space-x-1">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              className="w-2 h-2 bg-gray-400 rounded-full"
              animate={{
                y: [0, -8, 0],
                opacity: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.2,
                ease: "easeInOut",
              }}
            />
          ))}
        </div>
        <span className="text-xs text-gray-500 ml-2">Assistant is typing...</span>
      </div>
    </div>
  )
}

export default TypingIndicator
