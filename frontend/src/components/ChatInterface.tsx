import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { MessageCircle, X, Send, Loader2 } from 'lucide-react'
import { useChatStore } from '../store/chatStore'
import ProductCard from './ProductCard'
import CategoryChip from './CategoryChip'
import TypingIndicator from './TypingIndicator'

const ChatInterface: React.FC = () => {
  const {
    isOpen,
    messages,
    isLoading,
    suggestedProducts,
    suggestedCategories,
    openChat,
    closeChat,
    sendMessage,
  } = useChatStore()

  const [inputMessage, setInputMessage] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages, isLoading])

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isOpen])

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!inputMessage.trim() || isLoading) return

    const message = inputMessage.trim()
    setInputMessage('')
    await sendMessage(message)
  }

  const handleQuickResponse = async (message: string) => {
    await sendMessage(message)
  }

  const quickResponses = [
    "I'm looking for jeans",
    "Show me t-shirts",
    "I need formal wear",
    "What's on sale?",
    "Help me find a gift",
  ]

  if (!isOpen) {
    return (
      <motion.button
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => openChat()}
        className="fixed bottom-6 right-6 w-14 h-14 bg-primary-600 text-white rounded-full shadow-lg hover:bg-primary-700 transition-colors flex items-center justify-center z-50"
      >
        <MessageCircle className="w-6 h-6" />
      </motion.button>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="fixed bottom-6 right-6 w-96 h-[600px] bg-white rounded-lg shadow-2xl border border-gray-200 flex flex-col z-50"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-primary-600 text-white rounded-t-lg">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
            <MessageCircle className="w-4 h-4" />
          </div>
          <div>
            <h3 className="font-semibold">Shopping Assistant</h3>
            <p className="text-xs text-primary-100">Online now</p>
          </div>
        </div>
        <button
          onClick={closeChat}
          className="p-1 hover:bg-white/20 rounded-full transition-colors"
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 scrollbar-thin">
        {messages.length === 0 && (
          <div className="text-center text-gray-500 py-8">
            <MessageCircle className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p className="text-sm">
              Hi! I'm your shopping assistant. How can I help you find the perfect clothing today?
            </p>
            
            {/* Quick Start Options */}
            <div className="mt-6 space-y-2">
              <p className="text-xs font-medium text-gray-400 uppercase tracking-wide">
                Quick Start
              </p>
              <div className="flex flex-wrap gap-2">
                {quickResponses.map((response) => (
                  <button
                    key={response}
                    onClick={() => handleQuickResponse(response)}
                    className="text-xs px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors"
                  >
                    {response}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {messages.map((message, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`${
                message.role === 'user'
                  ? 'chat-message-user'
                  : 'chat-message-bot'
              }`}
            >
              {message.content}
            </div>
          </motion.div>
        ))}

        {isLoading && <TypingIndicator />}

        {/* Product Suggestions */}
        <AnimatePresence>
          {suggestedProducts.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-3"
            >
              <p className="text-sm font-medium text-gray-700">Recommended for you:</p>
              <div className="grid grid-cols-2 gap-2">
                {suggestedProducts.slice(0, 4).map((product) => (
                  <ProductCard key={product.id} product={product} compact />
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Category Suggestions */}
        <AnimatePresence>
          {suggestedCategories.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-3"
            >
              <p className="text-sm font-medium text-gray-700">Browse categories:</p>
              <div className="flex flex-wrap gap-2">
                {suggestedCategories.map((category) => (
                  <CategoryChip
                    key={category.id}
                    category={category}
                    onClick={() => handleQuickResponse(`Show me ${category.name.toLowerCase()}`)}
                  />
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <form onSubmit={handleSendMessage} className="p-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <input
            ref={inputRef}
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            placeholder="Type your message..."
            disabled={isLoading}
            className="flex-1 input text-sm"
          />
          <button
            type="submit"
            disabled={!inputMessage.trim() || isLoading}
            className="btn-primary p-2 disabled:opacity-50"
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </button>
        </div>
      </form>
    </motion.div>
  )
}

export default ChatInterface
