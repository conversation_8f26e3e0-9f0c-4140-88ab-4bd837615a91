import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { ChatMessage, ChatResponse, Product, Category } from '../types'

interface ChatState {
  isOpen: boolean
  messages: ChatMessage[]
  sessionToken: string | null
  isLoading: boolean
  suggestedProducts: Product[]
  suggestedCategories: Category[]
  
  // Actions
  openChat: (initialMessage?: string) => void
  closeChat: () => void
  toggleChat: () => void
  addMessage: (message: ChatMessage) => void
  setLoading: (loading: boolean) => void
  setSuggestions: (products: Product[], categories: Category[]) => void
  setSessionToken: (token: string) => void
  clearChat: () => void
  sendMessage: (message: string) => Promise<void>
}

export const useChatStore = create<ChatState>()(
  persist(
    (set, get) => ({
      isOpen: false,
      messages: [],
      sessionToken: null,
      isLoading: false,
      suggestedProducts: [],
      suggestedCategories: [],

      openChat: (initialMessage?: string) => {
        set({ isOpen: true })
        if (initialMessage) {
          get().sendMessage(initialMessage)
        }
      },

      closeChat: () => set({ isOpen: false }),

      toggleChat: () => set((state) => ({ isOpen: !state.isOpen })),

      addMessage: (message: ChatMessage) =>
        set((state) => ({
          messages: [...state.messages, message],
        })),

      setLoading: (loading: boolean) => set({ isLoading: loading }),

      setSuggestions: (products: Product[], categories: Category[]) =>
        set({
          suggestedProducts: products,
          suggestedCategories: categories,
        }),

      setSessionToken: (token: string) => set({ sessionToken: token }),

      clearChat: () =>
        set({
          messages: [],
          sessionToken: null,
          suggestedProducts: [],
          suggestedCategories: [],
        }),

      sendMessage: async (message: string) => {
        const { sessionToken, addMessage, setLoading, setSuggestions, setSessionToken } = get()
        
        // Add user message
        const userMessage: ChatMessage = {
          role: 'user',
          content: message,
          timestamp: new Date().toISOString(),
        }
        addMessage(userMessage)
        setLoading(true)

        try {
          const response = await fetch('/api/v1/chat/', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              message,
              session_token: sessionToken,
            }),
          })

          if (!response.ok) {
            throw new Error('Failed to send message')
          }

          const data: ChatResponse = await response.json()

          // Add bot response
          const botMessage: ChatMessage = {
            role: 'assistant',
            content: data.message,
            timestamp: new Date().toISOString(),
          }
          addMessage(botMessage)

          // Update session token
          setSessionToken(data.session_token)

          // Update suggestions
          setSuggestions(
            data.suggested_products || [],
            data.suggested_categories || []
          )
        } catch (error) {
          console.error('Error sending message:', error)
          
          // Add error message
          const errorMessage: ChatMessage = {
            role: 'assistant',
            content: "I'm sorry, I'm having trouble connecting right now. Please try again in a moment.",
            timestamp: new Date().toISOString(),
          }
          addMessage(errorMessage)
        } finally {
          setLoading(false)
        }
      },
    }),
    {
      name: 'chat-store',
      partialize: (state) => ({
        messages: state.messages,
        sessionToken: state.sessionToken,
      }),
    }
  )
)
