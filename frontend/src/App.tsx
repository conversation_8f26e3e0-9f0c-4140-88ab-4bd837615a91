import React from 'react'
import { Routes, Route } from 'react-router-dom'
import ChatInterface from './components/ChatInterface'
import ProductGrid from './components/ProductGrid'
import Header from './components/Header'
import { useChatStore } from './store/chatStore'

function App() {
  const { isOpen } = useChatStore()

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/products" element={<ProductGrid />} />
          <Route path="/chat" element={<ChatInterface />} />
        </Routes>
      </main>

      {/* Floating Chat Button */}
      {!isOpen && (
        <ChatInterface />
      )}
    </div>
  )
}

function HomePage() {
  const { openChat } = useChatStore()

  return (
    <div className="max-w-4xl mx-auto text-center">
      {/* Hero Section */}
      <div className="mb-12">
        <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
          Your Smart Shopping
          <span className="text-primary-600"> Assistant</span>
        </h1>
        <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
          Find the perfect clothing with our AI-powered bot. Get personalized recommendations 
          based on your style, size, and budget preferences.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button 
            onClick={openChat}
            className="btn-primary px-8 py-3 text-lg"
          >
            Start Shopping Chat
          </button>
          <a 
            href="/products" 
            className="btn-secondary px-8 py-3 text-lg"
          >
            Browse Products
          </a>
        </div>
      </div>

      {/* Features */}
      <div className="grid md:grid-cols-3 gap-8 mb-12">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold mb-2">Smart Conversations</h3>
          <p className="text-gray-600">Our AI understands your needs and asks the right questions to find perfect matches.</p>
        </div>
        
        <div className="text-center">
          <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold mb-2">Personalized Picks</h3>
          <p className="text-gray-600">Get recommendations tailored to your style, size, budget, and occasion.</p>
        </div>
        
        <div className="text-center">
          <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold mb-2">Lightning Fast</h3>
          <p className="text-gray-600">Find what you're looking for in seconds, not hours of browsing.</p>
        </div>
      </div>

      {/* Quick Start Categories */}
      <div className="bg-white rounded-lg p-8 shadow-sm">
        <h2 className="text-2xl font-semibold mb-6">Popular Categories</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[
            { name: "Men's Clothing", emoji: "👔" },
            { name: "Women's Clothing", emoji: "👗" },
            { name: "Footwear", emoji: "👟" },
            { name: "Accessories", emoji: "👜" }
          ].map((category) => (
            <button
              key={category.name}
              onClick={() => openChat(`I'm looking for ${category.name.toLowerCase()}`)}
              className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
            >
              <div className="text-2xl mb-2">{category.emoji}</div>
              <div className="font-medium">{category.name}</div>
            </button>
          ))}
        </div>
      </div>
    </div>
  )
}

export default App
