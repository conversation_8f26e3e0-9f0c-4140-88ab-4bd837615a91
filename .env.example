# Database Configuration
DATABASE_URL=postgresql://retail_user:retail_password@localhost:5432/retail_bot_db

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Local AI Configuration (Ollama) - Primary AI service
OLLAMA_URL=http://localhost:11434
LOCAL_AI_MODEL=llama3.2:3b

# OpenAI Configuration (Optional fallback - not required)
# OPENAI_API_KEY=your_openai_api_key_here

# API Configuration
API_TITLE=Retail Clothing Bot API
API_VERSION=1.0.0
API_DESCRIPTION=Smart retail clothing customer service bot API

# CORS Configuration
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# Session Configuration
SESSION_EXPIRE_HOURS=24

# Cache TTL (seconds)
CACHE_TTL_CATEGORIES=3600
CACHE_TTL_PRODUCTS=1800
CACHE_TTL_SEARCH=600
