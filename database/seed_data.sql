-- Seed Data for Retail Clothing Bot
-- 3-Level Category Hierarchy + 50+ Products

-- Insert Brands
INSERT INTO brands (name, slug, description) VALUES
('Nike', 'nike', 'Just Do It - Athletic wear and footwear'),
('Adidas', 'adidas', 'Impossible is Nothing - Sports and lifestyle'),
('Zara', 'zara', 'Fast fashion and contemporary clothing'),
('H&M', 'hm', 'Fashion and quality at the best price'),
('Uniqlo', 'uniqlo', 'LifeWear - Simple, high-quality everyday clothing'),
('Levi''s', 'levis', 'The original jeans company'),
('Calvin Klein', 'calvin-klein', 'Modern American luxury'),
('<PERSON> Hilfiger', 'tommy-hilfiger', 'Classic American cool'),
('Gap', 'gap', 'Casual wear for the whole family'),
('Old Navy', 'old-navy', 'Fun, fashion and value for families');

-- Level 1 Categories (Main Categories)
INSERT INTO categories (name, slug, level, description) VALUES
('Men''s Clothing', 'mens', 1, 'Complete range of men''s fashion and apparel'),
('Women''s Clothing', 'womens', 1, 'Trendy and stylish women''s fashion collection'),
('Kids'' Clothing', 'kids', 1, 'Comfortable and fun clothing for children');

-- Level 2 Categories (Sub Categories)
INSERT INTO categories (name, slug, level, parent_id, description) VALUES
-- Men's L2
('Tops', 'mens-tops', 2, (SELECT id FROM categories WHERE slug = 'mens'), 'Men''s shirts, t-shirts, and upper wear'),
('Bottoms', 'mens-bottoms', 2, (SELECT id FROM categories WHERE slug = 'mens'), 'Men''s pants, jeans, and shorts'),
('Outerwear', 'mens-outerwear', 2, (SELECT id FROM categories WHERE slug = 'mens'), 'Men''s jackets, coats, and hoodies'),
('Footwear', 'mens-footwear', 2, (SELECT id FROM categories WHERE slug = 'mens'), 'Men''s shoes, sneakers, and boots'),
('Accessories', 'mens-accessories', 2, (SELECT id FROM categories WHERE slug = 'mens'), 'Men''s watches, belts, and accessories'),

-- Women's L2
('Tops', 'womens-tops', 2, (SELECT id FROM categories WHERE slug = 'womens'), 'Women''s blouses, t-shirts, and tops'),
('Bottoms', 'womens-bottoms', 2, (SELECT id FROM categories WHERE slug = 'womens'), 'Women''s pants, jeans, and skirts'),
('Dresses', 'womens-dresses', 2, (SELECT id FROM categories WHERE slug = 'womens'), 'Women''s dresses and jumpsuits'),
('Outerwear', 'womens-outerwear', 2, (SELECT id FROM categories WHERE slug = 'womens'), 'Women''s jackets, coats, and cardigans'),
('Footwear', 'womens-footwear', 2, (SELECT id FROM categories WHERE slug = 'womens'), 'Women''s shoes, heels, and sneakers'),
('Accessories', 'womens-accessories', 2, (SELECT id FROM categories WHERE slug = 'womens'), 'Women''s jewelry, bags, and accessories'),

-- Kids' L2
('Boys', 'kids-boys', 2, (SELECT id FROM categories WHERE slug = 'kids'), 'Clothing for boys'),
('Girls', 'kids-girls', 2, (SELECT id FROM categories WHERE slug = 'kids'), 'Clothing for girls'),
('Baby', 'kids-baby', 2, (SELECT id FROM categories WHERE slug = 'kids'), 'Baby clothing and essentials');

-- Level 3 Categories (Specific Items)
INSERT INTO categories (name, slug, level, parent_id, description) VALUES
-- Men's Tops L3
('T-Shirts', 'mens-tshirts', 3, (SELECT id FROM categories WHERE slug = 'mens-tops'), 'Casual and graphic t-shirts'),
('Dress Shirts', 'mens-dress-shirts', 3, (SELECT id FROM categories WHERE slug = 'mens-tops'), 'Formal and business shirts'),
('Polo Shirts', 'mens-polo', 3, (SELECT id FROM categories WHERE slug = 'mens-tops'), 'Polo and golf shirts'),
('Tank Tops', 'mens-tanks', 3, (SELECT id FROM categories WHERE slug = 'mens-tops'), 'Sleeveless shirts and tanks'),

-- Men's Bottoms L3
('Jeans', 'mens-jeans', 3, (SELECT id FROM categories WHERE slug = 'mens-bottoms'), 'Denim jeans in various fits'),
('Chinos', 'mens-chinos', 3, (SELECT id FROM categories WHERE slug = 'mens-bottoms'), 'Casual chino pants'),
('Shorts', 'mens-shorts', 3, (SELECT id FROM categories WHERE slug = 'mens-bottoms'), 'Casual and athletic shorts'),
('Dress Pants', 'mens-dress-pants', 3, (SELECT id FROM categories WHERE slug = 'mens-bottoms'), 'Formal trousers'),

-- Men's Footwear L3
('Sneakers', 'mens-sneakers', 3, (SELECT id FROM categories WHERE slug = 'mens-footwear'), 'Athletic and casual sneakers'),
('Dress Shoes', 'mens-dress-shoes', 3, (SELECT id FROM categories WHERE slug = 'mens-footwear'), 'Formal leather shoes'),
('Boots', 'mens-boots', 3, (SELECT id FROM categories WHERE slug = 'mens-footwear'), 'Casual and work boots'),

-- Women's Tops L3
('T-Shirts', 'womens-tshirts', 3, (SELECT id FROM categories WHERE slug = 'womens-tops'), 'Casual and fitted t-shirts'),
('Blouses', 'womens-blouses', 3, (SELECT id FROM categories WHERE slug = 'womens-tops'), 'Elegant blouses and shirts'),
('Tank Tops', 'womens-tanks', 3, (SELECT id FROM categories WHERE slug = 'womens-tops'), 'Sleeveless tops and camisoles'),
('Sweaters', 'womens-sweaters', 3, (SELECT id FROM categories WHERE slug = 'womens-tops'), 'Knit sweaters and cardigans'),

-- Women's Bottoms L3
('Jeans', 'womens-jeans', 3, (SELECT id FROM categories WHERE slug = 'womens-bottoms'), 'Skinny, straight, and wide-leg jeans'),
('Leggings', 'womens-leggings', 3, (SELECT id FROM categories WHERE slug = 'womens-bottoms'), 'Comfortable leggings and yoga pants'),
('Skirts', 'womens-skirts', 3, (SELECT id FROM categories WHERE slug = 'womens-bottoms'), 'Mini, midi, and maxi skirts'),

-- Women's Footwear L3
('Sneakers', 'womens-sneakers', 3, (SELECT id FROM categories WHERE slug = 'womens-footwear'), 'Athletic and fashion sneakers'),
('Heels', 'womens-heels', 3, (SELECT id FROM categories WHERE slug = 'womens-footwear'), 'High heels and pumps'),
('Flats', 'womens-flats', 3, (SELECT id FROM categories WHERE slug = 'womens-footwear'), 'Ballet flats and loafers');

-- Insert Products (50+ items across categories)
INSERT INTO products (name, slug, description, short_description, brand_id, category_l1_id, category_l2_id, category_l3_id, base_price, sale_price, sku, stock_quantity, material, tags) VALUES

-- Men's T-Shirts
('Classic Cotton T-Shirt', 'mens-classic-cotton-tee', 'Comfortable everyday cotton t-shirt with a relaxed fit', 'Soft cotton tee for daily wear',
 (SELECT id FROM brands WHERE slug = 'uniqlo'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-tops'),
 (SELECT id FROM categories WHERE slug = 'mens-tshirts'),
 19.99, 14.99, 'MEN-TEE-001', 150, '100% Cotton', ARRAY['casual', 'cotton', 'basic']),

('Nike Dri-FIT Training Tee', 'nike-dri-fit-training-tee', 'Moisture-wicking athletic t-shirt for workouts', 'Performance tee with sweat-wicking technology',
 (SELECT id FROM brands WHERE slug = 'nike'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-tops'),
 (SELECT id FROM categories WHERE slug = 'mens-tshirts'),
 29.99, NULL, 'MEN-TEE-002', 200, 'Polyester blend', ARRAY['athletic', 'performance', 'workout']),

('Graphic Print T-Shirt', 'mens-graphic-print-tee', 'Trendy graphic t-shirt with modern design', 'Eye-catching graphic tee for casual style',
 (SELECT id FROM brands WHERE slug = 'hm'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-tops'),
 (SELECT id FROM categories WHERE slug = 'mens-tshirts'),
 24.99, 19.99, 'MEN-TEE-003', 120, 'Cotton blend', ARRAY['graphic', 'trendy', 'casual']),

-- Men's Jeans
('Levi''s 501 Original Jeans', 'levis-501-original-jeans', 'The original straight fit jeans with button fly', 'Iconic straight-leg jeans with timeless style',
 (SELECT id FROM brands WHERE slug = 'levis'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-bottoms'),
 (SELECT id FROM categories WHERE slug = 'mens-jeans'),
 89.99, 69.99, 'MEN-JEAN-001', 80, 'Denim', ARRAY['classic', 'straight-fit', 'denim']),

('Slim Fit Dark Wash Jeans', 'mens-slim-dark-wash-jeans', 'Modern slim fit jeans in dark indigo wash', 'Contemporary slim jeans for a sleek look',
 (SELECT id FROM brands WHERE slug = 'zara'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-bottoms'),
 (SELECT id FROM categories WHERE slug = 'mens-jeans'),
 59.99, NULL, 'MEN-JEAN-002', 100, 'Stretch denim', ARRAY['slim-fit', 'dark-wash', 'modern']),

-- Men's Sneakers
('Nike Air Max 90', 'nike-air-max-90', 'Classic running shoe with visible Air cushioning', 'Iconic sneaker with retro style and comfort',
 (SELECT id FROM brands WHERE slug = 'nike'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-footwear'),
 (SELECT id FROM categories WHERE slug = 'mens-sneakers'),
 119.99, 99.99, 'MEN-SHOE-001', 60, 'Leather and mesh', ARRAY['running', 'retro', 'air-max']),

('Adidas Stan Smith', 'adidas-stan-smith', 'Minimalist white leather tennis shoe', 'Clean and classic white sneaker',
 (SELECT id FROM brands WHERE slug = 'adidas'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-footwear'),
 (SELECT id FROM categories WHERE slug = 'mens-sneakers'),
 89.99, NULL, 'MEN-SHOE-002', 90, 'Leather', ARRAY['tennis', 'white', 'classic']),

-- Men's Dress Shirts
('Oxford Button-Down Shirt', 'mens-oxford-button-down', 'Classic oxford shirt perfect for business casual', 'Versatile dress shirt for office and casual wear',
 (SELECT id FROM brands WHERE slug = 'tommy-hilfiger'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-tops'),
 (SELECT id FROM categories WHERE slug = 'mens-dress-shirts'),
 79.99, 59.99, 'MEN-SHIRT-001', 70, 'Cotton oxford', ARRAY['business', 'oxford', 'button-down']),

-- Women's T-Shirts
('Soft Cotton V-Neck Tee', 'womens-soft-cotton-vneck', 'Flattering v-neck t-shirt in soft cotton', 'Comfortable everyday tee with feminine cut',
 (SELECT id FROM brands WHERE slug = 'gap'),
 (SELECT id FROM categories WHERE slug = 'womens'),
 (SELECT id FROM categories WHERE slug = 'womens-tops'),
 (SELECT id FROM categories WHERE slug = 'womens-tshirts'),
 22.99, 17.99, 'WOM-TEE-001', 180, '100% Cotton', ARRAY['v-neck', 'soft', 'casual']),

('Striped Long Sleeve Tee', 'womens-striped-long-sleeve', 'Classic striped long sleeve t-shirt', 'Timeless striped tee for layering',
 (SELECT id FROM brands WHERE slug = 'uniqlo'),
 (SELECT id FROM categories WHERE slug = 'womens'),
 (SELECT id FROM categories WHERE slug = 'womens-tops'),
 (SELECT id FROM categories WHERE slug = 'womens-tshirts'),
 29.99, NULL, 'WOM-TEE-002', 140, 'Cotton blend', ARRAY['striped', 'long-sleeve', 'classic']),

-- Women's Jeans
('High-Waisted Skinny Jeans', 'womens-high-waisted-skinny', 'Flattering high-waisted skinny jeans', 'Modern skinny jeans with high waist',
 (SELECT id FROM brands WHERE slug = 'zara'),
 (SELECT id FROM categories WHERE slug = 'womens'),
 (SELECT id FROM categories WHERE slug = 'womens-bottoms'),
 (SELECT id FROM categories WHERE slug = 'womens-jeans'),
 49.99, 39.99, 'WOM-JEAN-001', 110, 'Stretch denim', ARRAY['high-waisted', 'skinny', 'stretch']),

('Mom Jeans Vintage Wash', 'womens-mom-jeans-vintage', 'Relaxed fit mom jeans with vintage wash', 'Comfortable vintage-style mom jeans',
 (SELECT id FROM brands WHERE slug = 'hm'),
 (SELECT id FROM categories WHERE slug = 'womens'),
 (SELECT id FROM categories WHERE slug = 'womens-bottoms'),
 (SELECT id FROM categories WHERE slug = 'womens-jeans'),
 39.99, 29.99, 'WOM-JEAN-002', 95, 'Cotton denim', ARRAY['mom-jeans', 'vintage', 'relaxed']),

-- Women's Sneakers
('Nike Air Force 1', 'womens-nike-air-force-1', 'Classic white basketball sneaker for women', 'Iconic white sneaker with timeless appeal',
 (SELECT id FROM brands WHERE slug = 'nike'),
 (SELECT id FROM categories WHERE slug = 'womens'),
 (SELECT id FROM categories WHERE slug = 'womens-footwear'),
 (SELECT id FROM categories WHERE slug = 'womens-sneakers'),
 109.99, 89.99, 'WOM-SHOE-001', 75, 'Leather', ARRAY['basketball', 'white', 'classic']),

-- Women's Blouses
('Silk Button-Up Blouse', 'womens-silk-button-blouse', 'Elegant silk blouse for professional wear', 'Luxurious silk blouse for office and events',
 (SELECT id FROM brands WHERE slug = 'calvin-klein'),
 (SELECT id FROM categories WHERE slug = 'womens'),
 (SELECT id FROM categories WHERE slug = 'womens-tops'),
 (SELECT id FROM categories WHERE slug = 'womens-blouses'),
 129.99, 99.99, 'WOM-BLOUSE-001', 45, 'Silk', ARRAY['silk', 'professional', 'elegant']),

-- Women's Dresses
('Little Black Dress', 'womens-little-black-dress', 'Classic black dress perfect for any occasion', 'Versatile black dress for day to night',
 (SELECT id FROM brands WHERE slug = 'zara'),
 (SELECT id FROM categories WHERE slug = 'womens'),
 (SELECT id FROM categories WHERE slug = 'womens-dresses'),
 NULL,
 79.99, 59.99, 'WOM-DRESS-001', 60, 'Polyester blend', ARRAY['black-dress', 'versatile', 'classic']),

('Floral Summer Dress', 'womens-floral-summer-dress', 'Light and airy floral dress for summer', 'Breezy floral dress perfect for warm weather',
 (SELECT id FROM brands WHERE slug = 'hm'),
 (SELECT id FROM categories WHERE slug = 'womens'),
 (SELECT id FROM categories WHERE slug = 'womens-dresses'),
 NULL,
 49.99, 34.99, 'WOM-DRESS-002', 85, 'Cotton voile', ARRAY['floral', 'summer', 'light']),

-- Men's Chinos
('Khaki Chino Pants', 'mens-khaki-chino-pants', 'Classic khaki chinos for smart casual wear', 'Versatile chinos for work and weekend',
 (SELECT id FROM brands WHERE slug = 'gap'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-bottoms'),
 (SELECT id FROM categories WHERE slug = 'mens-chinos'),
 59.99, 44.99, 'MEN-CHINO-001', 120, 'Cotton twill', ARRAY['khaki', 'smart-casual', 'versatile']),

-- Men's Polo Shirts
('Classic Polo Shirt', 'mens-classic-polo-shirt', 'Timeless polo shirt in various colors', 'Essential polo for casual and golf wear',
 (SELECT id FROM brands WHERE slug = 'tommy-hilfiger'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-tops'),
 (SELECT id FROM categories WHERE slug = 'mens-polo'),
 69.99, NULL, 'MEN-POLO-001', 100, 'Cotton pique', ARRAY['polo', 'classic', 'golf']),

-- Women's Leggings
('High-Waisted Yoga Leggings', 'womens-yoga-leggings', 'Stretchy leggings perfect for yoga and workouts', 'Comfortable athletic leggings with high waist',
 (SELECT id FROM brands WHERE slug = 'nike'),
 (SELECT id FROM categories WHERE slug = 'womens'),
 (SELECT id FROM categories WHERE slug = 'womens-bottoms'),
 (SELECT id FROM categories WHERE slug = 'womens-leggings'),
 79.99, 59.99, 'WOM-LEG-001', 130, 'Polyester spandex', ARRAY['yoga', 'athletic', 'high-waisted']),

-- Men's Shorts
('Cargo Shorts', 'mens-cargo-shorts', 'Practical cargo shorts with multiple pockets', 'Utility shorts perfect for outdoor activities',
 (SELECT id FROM brands WHERE slug = 'old-navy'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-bottoms'),
 (SELECT id FROM categories WHERE slug = 'mens-shorts'),
 34.99, 24.99, 'MEN-SHORT-001', 90, 'Cotton canvas', ARRAY['cargo', 'utility', 'outdoor']),

('Athletic Basketball Shorts', 'mens-basketball-shorts', 'Mesh basketball shorts for sports and gym', 'Breathable shorts for athletic activities',
 (SELECT id FROM brands WHERE slug = 'nike'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-bottoms'),
 (SELECT id FROM categories WHERE slug = 'mens-shorts'),
 39.99, NULL, 'MEN-SHORT-002', 110, 'Polyester mesh', ARRAY['basketball', 'athletic', 'mesh']),

-- Women's Sweaters
('Cashmere Cardigan', 'womens-cashmere-cardigan', 'Luxurious cashmere cardigan sweater', 'Soft and warm cashmere cardigan',
 (SELECT id FROM brands WHERE slug = 'uniqlo'),
 (SELECT id FROM categories WHERE slug = 'womens'),
 (SELECT id FROM categories WHERE slug = 'womens-tops'),
 (SELECT id FROM categories WHERE slug = 'womens-sweaters'),
 199.99, 149.99, 'WOM-CARD-001', 40, 'Cashmere', ARRAY['cashmere', 'luxury', 'cardigan']),

-- Men's Dress Shoes
('Oxford Leather Shoes', 'mens-oxford-leather-shoes', 'Classic black oxford shoes for formal wear', 'Elegant leather shoes for business and formal events',
 (SELECT id FROM brands WHERE slug = 'calvin-klein'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-footwear'),
 (SELECT id FROM categories WHERE slug = 'mens-dress-shoes'),
 159.99, 119.99, 'MEN-OXFORD-001', 50, 'Genuine leather', ARRAY['oxford', 'formal', 'leather']),

-- Women's Heels
('Black Pumps', 'womens-black-pumps', 'Classic black pumps with 3-inch heel', 'Elegant pumps perfect for office and evening',
 (SELECT id FROM brands WHERE slug = 'calvin-klein'),
 (SELECT id FROM categories WHERE slug = 'womens'),
 (SELECT id FROM categories WHERE slug = 'womens-footwear'),
 (SELECT id FROM categories WHERE slug = 'womens-heels'),
 129.99, 99.99, 'WOM-PUMP-001', 65, 'Leather', ARRAY['pumps', 'black', 'office']),

-- Men's Tank Tops
('Basic Tank Top', 'mens-basic-tank-top', 'Simple cotton tank top for layering', 'Essential tank top for summer and layering',
 (SELECT id FROM brands WHERE slug = 'hm'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-tops'),
 (SELECT id FROM categories WHERE slug = 'mens-tanks'),
 12.99, 9.99, 'MEN-TANK-001', 200, 'Cotton', ARRAY['tank', 'basic', 'summer']),

-- Women's Tank Tops
('Ribbed Tank Top', 'womens-ribbed-tank-top', 'Fitted ribbed tank top in neutral colors', 'Versatile tank top for layering and solo wear',
 (SELECT id FROM brands WHERE slug = 'gap'),
 (SELECT id FROM categories WHERE slug = 'womens'),
 (SELECT id FROM categories WHERE slug = 'womens-tops'),
 (SELECT id FROM categories WHERE slug = 'womens-tanks'),
 18.99, 14.99, 'WOM-TANK-001', 160, 'Cotton blend', ARRAY['ribbed', 'fitted', 'layering']),

-- Additional Products to reach 50+
-- Women's Skirts
('Pleated Mini Skirt', 'womens-pleated-mini-skirt', 'Trendy pleated mini skirt', 'Cute pleated skirt for casual and dressy looks',
 (SELECT id FROM brands WHERE slug = 'zara'),
 (SELECT id FROM categories WHERE slug = 'womens'),
 (SELECT id FROM categories WHERE slug = 'womens-bottoms'),
 (SELECT id FROM categories WHERE slug = 'womens-skirts'),
 39.99, 29.99, 'WOM-SKIRT-001', 80, 'Polyester', ARRAY['pleated', 'mini', 'trendy']),

('Denim Midi Skirt', 'womens-denim-midi-skirt', 'Classic denim skirt in midi length', 'Versatile denim skirt for casual wear',
 (SELECT id FROM brands WHERE slug = 'levis'),
 (SELECT id FROM categories WHERE slug = 'womens'),
 (SELECT id FROM categories WHERE slug = 'womens-bottoms'),
 (SELECT id FROM categories WHERE slug = 'womens-skirts'),
 69.99, 54.99, 'WOM-SKIRT-002', 70, 'Denim', ARRAY['denim', 'midi', 'casual']),

-- Men's Boots
('Chelsea Boots', 'mens-chelsea-boots', 'Classic leather Chelsea boots', 'Stylish ankle boots perfect for any season',
 (SELECT id FROM brands WHERE slug = 'calvin-klein'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-footwear'),
 (SELECT id FROM categories WHERE slug = 'mens-boots'),
 189.99, 149.99, 'MEN-BOOT-001', 35, 'Leather', ARRAY['chelsea', 'ankle', 'leather']),

('Work Boots', 'mens-work-boots', 'Durable work boots with steel toe', 'Heavy-duty boots for construction and outdoor work',
 (SELECT id FROM brands WHERE slug = 'old-navy'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-footwear'),
 (SELECT id FROM categories WHERE slug = 'mens-boots'),
 129.99, NULL, 'MEN-BOOT-002', 45, 'Leather and steel', ARRAY['work', 'steel-toe', 'durable']),

-- Women's Flats
('Ballet Flats', 'womens-ballet-flats', 'Comfortable ballet flats for everyday wear', 'Classic flats perfect for work and casual',
 (SELECT id FROM brands WHERE slug = 'gap'),
 (SELECT id FROM categories WHERE slug = 'womens'),
 (SELECT id FROM categories WHERE slug = 'womens-footwear'),
 (SELECT id FROM categories WHERE slug = 'womens-flats'),
 59.99, 44.99, 'WOM-FLAT-001', 90, 'Synthetic leather', ARRAY['ballet', 'comfortable', 'everyday']),

-- Men's Dress Pants
('Wool Dress Trousers', 'mens-wool-dress-trousers', 'Premium wool dress pants for formal occasions', 'Elegant wool trousers for business and formal wear',
 (SELECT id FROM brands WHERE slug = 'tommy-hilfiger'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-bottoms'),
 (SELECT id FROM categories WHERE slug = 'mens-dress-pants'),
 149.99, 119.99, 'MEN-PANT-001', 55, 'Wool blend', ARRAY['wool', 'formal', 'business']),

-- Additional Casual Items
('Hoodie Sweatshirt', 'unisex-hoodie-sweatshirt', 'Comfortable cotton hoodie for casual wear', 'Cozy hoodie perfect for lounging and casual outings',
 (SELECT id FROM brands WHERE slug = 'nike'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-outerwear'),
 NULL,
 79.99, 59.99, 'UNI-HOOD-001', 120, 'Cotton fleece', ARRAY['hoodie', 'casual', 'comfortable']),

('Denim Jacket', 'unisex-denim-jacket', 'Classic denim jacket in vintage wash', 'Timeless denim jacket for layering',
 (SELECT id FROM brands WHERE slug = 'levis'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-outerwear'),
 NULL,
 99.99, 79.99, 'UNI-JACKET-001', 65, 'Denim', ARRAY['denim', 'jacket', 'vintage']),

('Athletic Joggers', 'unisex-athletic-joggers', 'Comfortable joggers for workouts and lounging', 'Soft joggers perfect for gym and casual wear',
 (SELECT id FROM brands WHERE slug = 'adidas'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-bottoms'),
 NULL,
 69.99, 54.99, 'UNI-JOG-001', 100, 'Polyester blend', ARRAY['joggers', 'athletic', 'comfortable']),

('Canvas Sneakers', 'unisex-canvas-sneakers', 'Classic canvas sneakers in multiple colors', 'Versatile canvas shoes for everyday wear',
 (SELECT id FROM brands WHERE slug = 'old-navy'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-footwear'),
 (SELECT id FROM categories WHERE slug = 'mens-sneakers'),
 39.99, 29.99, 'UNI-CANVAS-001', 150, 'Canvas', ARRAY['canvas', 'versatile', 'everyday']),

-- Premium Items
('Leather Jacket', 'mens-leather-jacket', 'Genuine leather motorcycle jacket', 'Premium leather jacket with classic styling',
 (SELECT id FROM brands WHERE slug = 'calvin-klein'),
 (SELECT id FROM categories WHERE slug = 'mens'),
 (SELECT id FROM categories WHERE slug = 'mens-outerwear'),
 NULL,
 399.99, 299.99, 'MEN-LEATHER-001', 25, 'Genuine leather', ARRAY['leather', 'motorcycle', 'premium']),

('Designer Handbag', 'womens-designer-handbag', 'Elegant leather handbag for professional use', 'Sophisticated handbag perfect for work and events',
 (SELECT id FROM brands WHERE slug = 'calvin-klein'),
 (SELECT id FROM categories WHERE slug = 'womens'),
 (SELECT id FROM categories WHERE slug = 'womens-accessories'),
 NULL,
 249.99, 199.99, 'WOM-BAG-001', 30, 'Leather', ARRAY['handbag', 'professional', 'elegant']);

-- Insert Product Variants (sizes and colors)
INSERT INTO product_variants (product_id, variant_type, variant_value, stock_quantity) VALUES
-- T-shirt sizes
((SELECT id FROM products WHERE sku = 'MEN-TEE-001'), 'size', 'S', 30),
((SELECT id FROM products WHERE sku = 'MEN-TEE-001'), 'size', 'M', 50),
((SELECT id FROM products WHERE sku = 'MEN-TEE-001'), 'size', 'L', 40),
((SELECT id FROM products WHERE sku = 'MEN-TEE-001'), 'size', 'XL', 30),

-- T-shirt colors
((SELECT id FROM products WHERE sku = 'MEN-TEE-001'), 'color', 'White', 40),
((SELECT id FROM products WHERE sku = 'MEN-TEE-001'), 'color', 'Black', 35),
((SELECT id FROM products WHERE sku = 'MEN-TEE-001'), 'color', 'Navy', 30),
((SELECT id FROM products WHERE sku = 'MEN-TEE-001'), 'color', 'Gray', 45),

-- Jeans sizes
((SELECT id FROM products WHERE sku = 'MEN-JEAN-001'), 'size', '30x30', 15),
((SELECT id FROM products WHERE sku = 'MEN-JEAN-001'), 'size', '32x30', 20),
((SELECT id FROM products WHERE sku = 'MEN-JEAN-001'), 'size', '32x32', 25),
((SELECT id FROM products WHERE sku = 'MEN-JEAN-001'), 'size', '34x32', 20),

-- Shoe sizes
((SELECT id FROM products WHERE sku = 'MEN-SHOE-001'), 'size', '8', 10),
((SELECT id FROM products WHERE sku = 'MEN-SHOE-001'), 'size', '9', 15),
((SELECT id FROM products WHERE sku = 'MEN-SHOE-001'), 'size', '10', 20),
((SELECT id FROM products WHERE sku = 'MEN-SHOE-001'), 'size', '11', 15);

-- Insert Sample Product Images
INSERT INTO product_images (product_id, image_url, alt_text, is_primary, sort_order) VALUES
((SELECT id FROM products WHERE sku = 'MEN-TEE-001'), 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab', 'Classic Cotton T-Shirt - Front View', true, 1),
((SELECT id FROM products WHERE sku = 'MEN-TEE-001'), 'https://images.unsplash.com/photo-1583743814966-8936f37f4678', 'Classic Cotton T-Shirt - Back View', false, 2),

((SELECT id FROM products WHERE sku = 'MEN-JEAN-001'), 'https://images.unsplash.com/photo-1542272604-787c3835535d', 'Levi''s 501 Original Jeans - Front', true, 1),
((SELECT id FROM products WHERE sku = 'MEN-JEAN-001'), 'https://images.unsplash.com/photo-1475178626620-a4d074967452', 'Levi''s 501 Original Jeans - Detail', false, 2),

((SELECT id FROM products WHERE sku = 'MEN-SHOE-001'), 'https://images.unsplash.com/photo-1549298916-b41d501d3772', 'Nike Air Max 90 - Side View', true, 1),
((SELECT id FROM products WHERE sku = 'MEN-SHOE-001'), 'https://images.unsplash.com/photo-1460353581641-37baddab0fa2', 'Nike Air Max 90 - Top View', false, 2),

((SELECT id FROM products WHERE sku = 'WOM-TEE-001'), 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1', 'Women''s V-Neck Tee - Front', true, 1),
((SELECT id FROM products WHERE sku = 'WOM-JEAN-001'), 'https://images.unsplash.com/photo-1541099649105-f69ad21f3246', 'High-Waisted Skinny Jeans', true, 1),
((SELECT id FROM products WHERE sku = 'WOM-SHOE-001'), 'https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a', 'Nike Air Force 1 Women''s', true, 1);

-- Update product ratings and view counts for realism
UPDATE products SET
    rating_average = CASE
        WHEN RANDOM() < 0.3 THEN ROUND((RANDOM() * 2 + 3)::numeric, 1) -- 3.0-5.0 range
        ELSE ROUND((RANDOM() * 1.5 + 3.5)::numeric, 1) -- 3.5-5.0 range for most products
    END,
    rating_count = FLOOR(RANDOM() * 500 + 10)::integer,
    view_count = FLOOR(RANDOM() * 5000 + 100)::integer,
    purchase_count = FLOOR(RANDOM() * 200 + 5)::integer
WHERE id IS NOT NULL;
