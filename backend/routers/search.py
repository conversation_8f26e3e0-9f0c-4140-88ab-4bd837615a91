from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import or_, and_, func, desc, asc
from typing import List, Optional
import json
import math

from database import get_db, get_redis
from models import Product as ProductModel, Brand, Category, ProductVariant
from schemas import SearchRequest, SearchResponse, ProductSummary, SearchFilters
from routers.products import get_product_summary
from config import settings

router = APIRouter()

@router.post("/", response_model=SearchResponse)
async def search_products(
    search_request: SearchRequest,
    db: Session = Depends(get_db),
    redis = Depends(get_redis)
):
    """Advanced product search with filters and sorting"""
    
    # Build cache key
    cache_key = f"search:{hash(str(search_request.model_dump()))}"
    
    # Try cache first
    cached_result = redis.get(cache_key)
    if cached_result:
        return SearchResponse.model_validate(json.loads(cached_result))
    
    # Build base query
    query = db.query(ProductModel).options(
        joinedload(ProductModel.brand),
        joinedload(ProductModel.category_l1),
        joinedload(ProductModel.category_l2),
        joinedload(ProductModel.category_l3),
        joinedload(ProductModel.images)
    ).filter(ProductModel.is_active == True)
    
    # Apply text search
    if search_request.query:
        search_terms = search_request.query.lower().split()
        search_conditions = []
        
        for term in search_terms:
            term_conditions = [
                ProductModel.name.ilike(f"%{term}%"),
                ProductModel.description.ilike(f"%{term}%"),
                ProductModel.short_description.ilike(f"%{term}%"),
                ProductModel.tags.any(func.lower(func.unnest(ProductModel.tags)).like(f"%{term}%"))
            ]
            search_conditions.append(or_(*term_conditions))
        
        if search_conditions:
            query = query.filter(and_(*search_conditions))
    
    # Apply filters
    if search_request.filters:
        filters = search_request.filters
        
        # Category filters
        if filters.category_l1:
            query = query.join(Category, ProductModel.category_l1_id == Category.id).filter(
                Category.slug == filters.category_l1
            )
        
        if filters.category_l2:
            query = query.join(Category, ProductModel.category_l2_id == Category.id).filter(
                Category.slug == filters.category_l2
            )
        
        if filters.category_l3:
            query = query.join(Category, ProductModel.category_l3_id == Category.id).filter(
                Category.slug == filters.category_l3
            )
        
        # Brand filter
        if filters.brand:
            query = query.join(Brand).filter(Brand.slug == filters.brand)
        
        # Price filters
        if filters.min_price is not None:
            query = query.filter(
                or_(
                    and_(ProductModel.sale_price.is_not(None), ProductModel.sale_price >= filters.min_price),
                    and_(ProductModel.sale_price.is_(None), ProductModel.base_price >= filters.min_price)
                )
            )
        
        if filters.max_price is not None:
            query = query.filter(
                or_(
                    and_(ProductModel.sale_price.is_not(None), ProductModel.sale_price <= filters.max_price),
                    and_(ProductModel.sale_price.is_(None), ProductModel.base_price <= filters.max_price)
                )
            )
        
        # Stock filter
        if filters.in_stock:
            query = query.filter(ProductModel.stock_quantity > 0)
        
        # Size and color filters (through variants)
        if filters.size or filters.color:
            variant_conditions = []
            
            if filters.size:
                variant_conditions.append(
                    and_(
                        ProductVariant.variant_type == 'size',
                        ProductVariant.variant_value.ilike(f"%{filters.size}%"),
                        ProductVariant.is_active == True
                    )
                )
            
            if filters.color:
                variant_conditions.append(
                    and_(
                        ProductVariant.variant_type == 'color',
                        ProductVariant.variant_value.ilike(f"%{filters.color}%"),
                        ProductVariant.is_active == True
                    )
                )
            
            if variant_conditions:
                query = query.join(ProductVariant).filter(or_(*variant_conditions))
        
        # Tags filter
        if filters.tags:
            for tag in filters.tags:
                query = query.filter(ProductModel.tags.any(func.lower(func.unnest(ProductModel.tags)).like(f"%{tag.lower()}%")))
    
    # Get total count before pagination
    total_count = query.count()
    
    # Apply sorting
    if search_request.sort_by == "price_asc":
        query = query.order_by(
            asc(func.coalesce(ProductModel.sale_price, ProductModel.base_price))
        )
    elif search_request.sort_by == "price_desc":
        query = query.order_by(
            desc(func.coalesce(ProductModel.sale_price, ProductModel.base_price))
        )
    elif search_request.sort_by == "rating":
        query = query.order_by(desc(ProductModel.rating_average), desc(ProductModel.rating_count))
    elif search_request.sort_by == "newest":
        query = query.order_by(desc(ProductModel.created_at))
    elif search_request.sort_by == "popularity":
        query = query.order_by(desc(ProductModel.view_count), desc(ProductModel.purchase_count))
    else:  # relevance (default)
        if search_request.query:
            # Simple relevance scoring based on name matches
            query = query.order_by(
                desc(func.similarity(ProductModel.name, search_request.query)),
                desc(ProductModel.rating_average),
                desc(ProductModel.view_count)
            )
        else:
            query = query.order_by(desc(ProductModel.is_featured), desc(ProductModel.rating_average))
    
    # Apply pagination
    offset = (search_request.page - 1) * search_request.page_size
    products = query.offset(offset).limit(search_request.page_size).all()
    
    # Convert to response format
    product_summaries = [get_product_summary(product) for product in products]
    
    # Calculate pagination info
    total_pages = math.ceil(total_count / search_request.page_size)
    
    # Build response
    response = SearchResponse(
        products=product_summaries,
        total_count=total_count,
        page=search_request.page,
        page_size=search_request.page_size,
        total_pages=total_pages,
        filters_applied=search_request.filters
    )
    
    # Cache the result
    redis.setex(cache_key, settings.cache_ttl_search, response.model_dump_json())
    
    return response

@router.get("/", response_model=SearchResponse)
async def search_products_get(
    q: Optional[str] = Query(None, description="Search query"),
    category_l1: Optional[str] = Query(None, description="Level 1 category slug"),
    category_l2: Optional[str] = Query(None, description="Level 2 category slug"),
    category_l3: Optional[str] = Query(None, description="Level 3 category slug"),
    brand: Optional[str] = Query(None, description="Brand slug"),
    min_price: Optional[float] = Query(None, description="Minimum price"),
    max_price: Optional[float] = Query(None, description="Maximum price"),
    size: Optional[str] = Query(None, description="Size filter"),
    color: Optional[str] = Query(None, description="Color filter"),
    in_stock: bool = Query(True, description="Only show in-stock items"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    sort_by: str = Query("relevance", description="Sort by: relevance, price_asc, price_desc, rating, newest, popularity"),
    db: Session = Depends(get_db),
    redis = Depends(get_redis)
):
    """GET endpoint for product search (for easier URL-based searches)"""
    
    # Build filters
    filters = SearchFilters(
        category_l1=category_l1,
        category_l2=category_l2,
        category_l3=category_l3,
        brand=brand,
        min_price=min_price,
        max_price=max_price,
        size=size,
        color=color,
        in_stock=in_stock
    )
    
    # Build search request
    search_request = SearchRequest(
        query=q,
        filters=filters,
        page=page,
        page_size=page_size,
        sort_by=sort_by
    )
    
    return await search_products(search_request, db, redis)
