from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
import json
import uuid
from datetime import datetime, timedelta

from database import get_db, get_redis
from models import CustomerSession
from schemas import ChatRequest, ChatResponse, ChatMessage, CustomerPreferences
from services.local_ai_service import LocalAIService
from services.learning_service import LearningService
from services.model_training_service import ModelTrainingService
from config import settings

router = APIRouter()
ai_service = LocalAIService()
learning_service = LearningService()
training_service = ModelTrainingService()

def generate_session_token() -> str:
    """Generate a unique session token"""
    return str(uuid.uuid4())

async def get_or_create_session(
    session_token: Optional[str],
    db: Session,
    redis
) -> CustomerSession:
    """Get existing session or create new one"""
    
    if session_token:
        # Try to get existing session
        session = db.query(CustomerSession).filter(
            CustomerSession.session_token == session_token,
            CustomerSession.expires_at > datetime.utcnow()
        ).first()
        
        if session:
            return session
    
    # Create new session
    new_token = generate_session_token()
    expires_at = datetime.utcnow() + timedelta(hours=settings.session_expire_hours)
    
    session = CustomerSession(
        session_token=new_token,
        customer_preferences={},
        conversation_history=[],
        current_context={},
        expires_at=expires_at
    )
    
    db.add(session)
    db.commit()
    db.refresh(session)
    
    return session

@router.post("/", response_model=ChatResponse)
async def chat_with_bot(
    request: ChatRequest,
    db: Session = Depends(get_db),
    redis = Depends(get_redis)
):
    """Main chat endpoint for conversing with the AI bot"""
    
    try:
        # Get or create session
        session = await get_or_create_session(request.session_token, db, redis)
        
        # Parse conversation history
        conversation_history = []
        if session.conversation_history:
            for msg_data in session.conversation_history:
                conversation_history.append(ChatMessage(**msg_data))
        
        # Parse customer preferences
        customer_preferences = None
        if session.customer_preferences:
            customer_preferences = CustomerPreferences(**session.customer_preferences)
        
        # Add user message to history
        user_message = ChatMessage(
            role="user",
            content=request.message,
            timestamp=datetime.now()
        )
        conversation_history.append(user_message)
        
        # Generate AI response
        ai_response, suggested_products, suggested_categories, updated_context = await ai_service.generate_response(
            request.message,
            conversation_history,
            customer_preferences,
            db
        )
        
        # Add AI response to history
        ai_message = ChatMessage(
            role="assistant",
            content=ai_response,
            timestamp=datetime.now()
        )
        conversation_history.append(ai_message)
        
        # Update customer preferences if needed
        if updated_context and "updated_preferences" in updated_context:
            new_prefs = updated_context["updated_preferences"]
            if customer_preferences:
                # Merge with existing preferences
                current_prefs = customer_preferences.model_dump()
                current_prefs.update(new_prefs)
                customer_preferences = CustomerPreferences(**current_prefs)
            else:
                customer_preferences = CustomerPreferences(**new_prefs)
        
        # Extract preferences from conversation if not already done
        if not customer_preferences:
            customer_preferences = await ai_service.extract_preferences(conversation_history)

        # Apply learning-based personalization to suggestions
        if suggested_products:
            suggested_products = learning_service.get_personalized_recommendations(
                session.session_token,
                suggested_products,
                customer_preferences
            )

        # Log interaction for learning
        learning_service.log_interaction(
            session_token=session.session_token,
            user_message=request.message,
            ai_response=ai_response,
            suggested_products=suggested_products,
            customer_preferences=customer_preferences,
            conversation_context=updated_context
        )

        # Update session in database
        session.conversation_history = [msg.model_dump() for msg in conversation_history[-20:]]  # Keep last 20 messages
        session.customer_preferences = customer_preferences.model_dump() if customer_preferences else {}
        session.current_context = updated_context or {}
        session.updated_at = datetime.utcnow()
        
        db.commit()
        
        # Build response
        response = ChatResponse(
            message=ai_response,
            session_token=session.session_token,
            suggested_products=suggested_products,
            suggested_categories=suggested_categories,
            context=updated_context
        )
        
        return response
        
    except Exception as e:
        # Log error and return fallback response
        print(f"Chat error: {str(e)}")
        
        # Create minimal session if needed
        if not request.session_token:
            session = await get_or_create_session(None, db, redis)
        else:
            session = await get_or_create_session(request.session_token, db, redis)
        
        fallback_response = "I apologize, but I'm having some technical difficulties. How can I help you find the perfect clothing today?"
        
        return ChatResponse(
            message=fallback_response,
            session_token=session.session_token,
            suggested_products=None,
            suggested_categories=None,
            context=None
        )

@router.get("/session/{session_token}", response_model=Dict[str, Any])
async def get_session_info(
    session_token: str,
    db: Session = Depends(get_db)
):
    """Get session information including conversation history and preferences"""
    
    session = db.query(CustomerSession).filter(
        CustomerSession.session_token == session_token,
        CustomerSession.expires_at > datetime.utcnow()
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found or expired"
        )
    
    return {
        "session_token": session.session_token,
        "conversation_history": session.conversation_history,
        "customer_preferences": session.customer_preferences,
        "current_context": session.current_context,
        "created_at": session.created_at,
        "updated_at": session.updated_at,
        "expires_at": session.expires_at
    }

@router.delete("/session/{session_token}")
async def clear_session(
    session_token: str,
    db: Session = Depends(get_db)
):
    """Clear/delete a chat session"""
    
    session = db.query(CustomerSession).filter(
        CustomerSession.session_token == session_token
    ).first()
    
    if session:
        db.delete(session)
        db.commit()
        return {"message": "Session cleared successfully"}
    
    return {"message": "Session not found"}

@router.post("/session/{session_token}/preferences", response_model=Dict[str, Any])
async def update_session_preferences(
    session_token: str,
    preferences: CustomerPreferences,
    db: Session = Depends(get_db)
):
    """Update customer preferences for a session"""
    
    session = db.query(CustomerSession).filter(
        CustomerSession.session_token == session_token,
        CustomerSession.expires_at > datetime.utcnow()
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found or expired"
        )
    
    session.customer_preferences = preferences.model_dump()
    session.updated_at = datetime.utcnow()
    db.commit()
    
    return {
        "message": "Preferences updated successfully",
        "preferences": session.customer_preferences
    }

@router.get("/suggestions/categories", response_model=List[Dict[str, Any]])
async def get_category_suggestions(
    level: int = 1,
    db: Session = Depends(get_db)
):
    """Get category suggestions for the chat interface"""
    
    from models import Category as CategoryModel
    
    categories = db.query(CategoryModel).filter(
        CategoryModel.is_active == True,
        CategoryModel.level == level
    ).order_by(CategoryModel.sort_order, CategoryModel.name).limit(6).all()
    
    return [
        {
            "id": str(cat.id),
            "name": cat.name,
            "slug": cat.slug,
            "description": cat.description
        }
        for cat in categories
    ]

@router.get("/suggestions/quick-responses")
async def get_quick_responses():
    """Get quick response suggestions for the chat interface"""

    return [
        "I'm looking for jeans",
        "Show me t-shirts",
        "I need formal wear",
        "What's on sale?",
        "I'm shopping for a gift",
        "Show me new arrivals",
        "I need help with sizing",
        "What's trending now?"
    ]

@router.get("/learning/insights")
async def get_learning_insights():
    """Get insights about the AI learning system performance"""

    return learning_service.get_learning_insights()

@router.post("/learning/feedback")
async def provide_feedback(
    session_token: str,
    feedback_type: str,  # 'helpful', 'not_helpful', 'wrong_product', 'good_recommendation'
    message_id: Optional[str] = None,
    product_id: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Provide feedback on AI responses for learning improvement"""

    # Store feedback for learning
    feedback_data = {
        'session_token': session_token,
        'feedback_type': feedback_type,
        'message_id': message_id,
        'product_id': product_id,
        'timestamp': datetime.utcnow().isoformat()
    }

    # This could be stored in database or used to update learning models
    learning_service.recommendation_feedback[f"{session_token}_{datetime.utcnow().timestamp()}"] = feedback_data

    return {"message": "Feedback received and will be used to improve recommendations"}

@router.get("/training/status")
async def get_training_status():
    """Get current model training status and statistics"""

    return await training_service.get_training_status()

@router.post("/training/start")
async def start_model_training():
    """Start the model training pipeline"""

    result = await training_service.run_training_pipeline()
    return result
