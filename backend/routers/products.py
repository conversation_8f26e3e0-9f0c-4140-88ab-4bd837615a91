from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session, joinedload
from typing import List, Optional
from uuid import UUID
import json

from database import get_db, get_redis
from models import Product as ProductModel, ProductImage, Brand, Category
from schemas import Product, ProductSummary
from config import settings

router = APIRouter()

def get_product_summary(product: ProductModel) -> ProductSummary:
    """Convert Product model to ProductSummary schema"""
    primary_image = None
    if product.images:
        primary_img = next((img for img in product.images if img.is_primary), None)
        if primary_img:
            primary_image = primary_img.image_url
        elif product.images:
            primary_image = product.images[0].image_url
    
    brand_name = product.brand.name if product.brand else None
    
    # Build category path
    category_path = None
    if product.category_l1:
        path_parts = [product.category_l1.name]
        if product.category_l2:
            path_parts.append(product.category_l2.name)
            if product.category_l3:
                path_parts.append(product.category_l3.name)
        category_path = " > ".join(path_parts)
    
    return ProductSummary(
        id=product.id,
        name=product.name,
        slug=product.slug,
        short_description=product.short_description,
        base_price=product.base_price,
        sale_price=product.sale_price,
        currency=product.currency,
        rating_average=product.rating_average,
        rating_count=product.rating_count,
        primary_image=primary_image,
        brand_name=brand_name,
        category_path=category_path
    )

@router.get("/", response_model=List[ProductSummary])
async def get_products(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    featured: Optional[bool] = None,
    category: Optional[str] = None,
    brand: Optional[str] = None,
    db: Session = Depends(get_db),
    redis = Depends(get_redis)
):
    """Get list of products with optional filters"""
    
    # Build cache key
    cache_key = f"products:skip={skip}:limit={limit}:featured={featured}:category={category}:brand={brand}"
    
    # Try to get from cache
    cached_result = redis.get(cache_key)
    if cached_result:
        return json.loads(cached_result)
    
    # Build query
    query = db.query(ProductModel).options(
        joinedload(ProductModel.brand),
        joinedload(ProductModel.category_l1),
        joinedload(ProductModel.category_l2),
        joinedload(ProductModel.category_l3),
        joinedload(ProductModel.images)
    ).filter(ProductModel.is_active == True)
    
    # Apply filters
    if featured is not None:
        query = query.filter(ProductModel.is_featured == featured)
    
    if category:
        query = query.join(Category, 
            (ProductModel.category_l1_id == Category.id) |
            (ProductModel.category_l2_id == Category.id) |
            (ProductModel.category_l3_id == Category.id)
        ).filter(Category.slug == category)
    
    if brand:
        query = query.join(Brand).filter(Brand.slug == brand)
    
    # Execute query
    products = query.offset(skip).limit(limit).all()
    
    # Convert to response format
    result = [get_product_summary(product) for product in products]
    
    # Cache the result
    redis.setex(cache_key, settings.cache_ttl_products, json.dumps([p.model_dump() for p in result], default=str))
    
    return result

@router.get("/{product_id}", response_model=Product)
async def get_product(
    product_id: UUID,
    db: Session = Depends(get_db),
    redis = Depends(get_redis)
):
    """Get detailed product information"""
    
    # Try cache first
    cache_key = f"product:{product_id}"
    cached_result = redis.get(cache_key)
    if cached_result:
        return json.loads(cached_result)
    
    # Query database
    product = db.query(ProductModel).options(
        joinedload(ProductModel.brand),
        joinedload(ProductModel.category_l1),
        joinedload(ProductModel.category_l2),
        joinedload(ProductModel.category_l3),
        joinedload(ProductModel.images),
        joinedload(ProductModel.variants)
    ).filter(
        ProductModel.id == product_id,
        ProductModel.is_active == True
    ).first()
    
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Product not found"
        )
    
    # Increment view count
    product.view_count += 1
    db.commit()
    
    # Convert to schema
    result = Product.model_validate(product)
    
    # Cache the result
    redis.setex(cache_key, settings.cache_ttl_products, result.model_dump_json())
    
    return result

@router.get("/slug/{slug}", response_model=Product)
async def get_product_by_slug(
    slug: str,
    db: Session = Depends(get_db),
    redis = Depends(get_redis)
):
    """Get product by slug"""
    
    # Try cache first
    cache_key = f"product:slug:{slug}"
    cached_result = redis.get(cache_key)
    if cached_result:
        return json.loads(cached_result)
    
    # Query database
    product = db.query(ProductModel).options(
        joinedload(ProductModel.brand),
        joinedload(ProductModel.category_l1),
        joinedload(ProductModel.category_l2),
        joinedload(ProductModel.category_l3),
        joinedload(ProductModel.images),
        joinedload(ProductModel.variants)
    ).filter(
        ProductModel.slug == slug,
        ProductModel.is_active == True
    ).first()
    
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Product not found"
        )
    
    # Increment view count
    product.view_count += 1
    db.commit()
    
    # Convert to schema
    result = Product.model_validate(product)
    
    # Cache the result
    redis.setex(cache_key, settings.cache_ttl_products, result.model_dump_json())
    
    return result

@router.get("/featured/", response_model=List[ProductSummary])
async def get_featured_products(
    limit: int = Query(10, ge=1, le=50),
    db: Session = Depends(get_db),
    redis = Depends(get_redis)
):
    """Get featured products"""
    return await get_products(skip=0, limit=limit, featured=True, db=db, redis=redis)
