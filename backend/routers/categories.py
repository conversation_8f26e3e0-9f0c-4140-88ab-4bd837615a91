from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
import json

from database import get_db, get_redis
from models import Category as CategoryModel
from schemas import Category, CategoryTree
from config import settings

router = APIRouter()

def build_category_tree(categories: List[CategoryModel], parent_id: Optional[UUID] = None) -> List[CategoryTree]:
    """Build hierarchical category tree"""
    tree = []
    for category in categories:
        if category.parent_id == parent_id:
            category_dict = CategoryTree.model_validate(category)
            category_dict.children = build_category_tree(categories, category.id)
            tree.append(category_dict)
    return tree

@router.get("/", response_model=List[Category])
async def get_categories(
    level: Optional[int] = None,
    parent_id: Optional[UUID] = None,
    db: Session = Depends(get_db),
    redis = Depends(get_redis)
):
    """Get categories with optional filtering by level or parent"""
    
    # Build cache key
    cache_key = f"categories:level={level}:parent={parent_id}"
    
    # Try cache first
    cached_result = redis.get(cache_key)
    if cached_result:
        return json.loads(cached_result)
    
    # Build query
    query = db.query(CategoryModel).filter(CategoryModel.is_active == True)
    
    if level is not None:
        query = query.filter(CategoryModel.level == level)
    
    if parent_id is not None:
        query = query.filter(CategoryModel.parent_id == parent_id)
    
    # Execute query
    categories = query.order_by(CategoryModel.sort_order, CategoryModel.name).all()
    
    # Convert to schema
    result = [Category.model_validate(cat) for cat in categories]
    
    # Cache result
    redis.setex(cache_key, settings.cache_ttl_categories, json.dumps([c.model_dump() for c in result], default=str))
    
    return result

@router.get("/tree", response_model=List[CategoryTree])
async def get_category_tree(
    db: Session = Depends(get_db),
    redis = Depends(get_redis)
):
    """Get complete category hierarchy as a tree"""
    
    cache_key = "categories:tree"
    
    # Try cache first
    cached_result = redis.get(cache_key)
    if cached_result:
        return json.loads(cached_result)
    
    # Get all categories
    categories = db.query(CategoryModel).filter(
        CategoryModel.is_active == True
    ).order_by(CategoryModel.level, CategoryModel.sort_order, CategoryModel.name).all()
    
    # Build tree structure
    tree = build_category_tree(categories)
    
    # Cache result
    redis.setex(cache_key, settings.cache_ttl_categories, json.dumps([t.model_dump() for t in tree], default=str))
    
    return tree

@router.get("/l1", response_model=List[Category])
async def get_l1_categories(db: Session = Depends(get_db), redis = Depends(get_redis)):
    """Get Level 1 (main) categories"""
    return await get_categories(level=1, db=db, redis=redis)

@router.get("/l2/{parent_slug}", response_model=List[Category])
async def get_l2_categories(
    parent_slug: str,
    db: Session = Depends(get_db),
    redis = Depends(get_redis)
):
    """Get Level 2 categories for a given L1 parent"""
    
    # Find parent category
    parent = db.query(CategoryModel).filter(
        CategoryModel.slug == parent_slug,
        CategoryModel.level == 1,
        CategoryModel.is_active == True
    ).first()
    
    if not parent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Parent category not found"
        )
    
    return await get_categories(level=2, parent_id=parent.id, db=db, redis=redis)

@router.get("/l3/{parent_slug}", response_model=List[Category])
async def get_l3_categories(
    parent_slug: str,
    db: Session = Depends(get_db),
    redis = Depends(get_redis)
):
    """Get Level 3 categories for a given L2 parent"""
    
    # Find parent category
    parent = db.query(CategoryModel).filter(
        CategoryModel.slug == parent_slug,
        CategoryModel.level == 2,
        CategoryModel.is_active == True
    ).first()
    
    if not parent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Parent category not found"
        )
    
    return await get_categories(level=3, parent_id=parent.id, db=db, redis=redis)

@router.get("/{category_id}", response_model=Category)
async def get_category(
    category_id: UUID,
    db: Session = Depends(get_db),
    redis = Depends(get_redis)
):
    """Get category by ID"""
    
    cache_key = f"category:{category_id}"
    
    # Try cache first
    cached_result = redis.get(cache_key)
    if cached_result:
        return json.loads(cached_result)
    
    # Query database
    category = db.query(CategoryModel).filter(
        CategoryModel.id == category_id,
        CategoryModel.is_active == True
    ).first()
    
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Category not found"
        )
    
    # Convert to schema
    result = Category.model_validate(category)
    
    # Cache result
    redis.setex(cache_key, settings.cache_ttl_categories, result.model_dump_json())
    
    return result

@router.get("/slug/{slug}", response_model=Category)
async def get_category_by_slug(
    slug: str,
    db: Session = Depends(get_db),
    redis = Depends(get_redis)
):
    """Get category by slug"""
    
    cache_key = f"category:slug:{slug}"
    
    # Try cache first
    cached_result = redis.get(cache_key)
    if cached_result:
        return json.loads(cached_result)
    
    # Query database
    category = db.query(CategoryModel).filter(
        CategoryModel.slug == slug,
        CategoryModel.is_active == True
    ).first()
    
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Category not found"
        )
    
    # Convert to schema
    result = Category.model_validate(category)
    
    # Cache result
    redis.setex(cache_key, settings.cache_ttl_categories, result.model_dump_json())
    
    return result
