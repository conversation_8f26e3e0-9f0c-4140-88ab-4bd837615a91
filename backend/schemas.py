from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List, Dict, Any
from decimal import Decimal
from datetime import datetime
from uuid import UUID

# Base schemas
class CategoryBase(BaseModel):
    name: str
    slug: str
    level: int
    description: Optional[str] = None
    image_url: Optional[str] = None
    is_active: bool = True
    sort_order: int = 0

class CategoryCreate(CategoryBase):
    parent_id: Optional[UUID] = None

class Category(CategoryBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    parent_id: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime

class CategoryTree(Category):
    children: List['CategoryTree'] = []

class BrandBase(BaseModel):
    name: str
    slug: str
    description: Optional[str] = None
    logo_url: Optional[str] = None
    is_active: bool = True

class Brand(BrandBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    created_at: datetime

class ProductImageBase(BaseModel):
    image_url: str
    alt_text: Optional[str] = None
    is_primary: bool = False
    sort_order: int = 0

class ProductImage(ProductImageBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    product_id: UUID
    created_at: datetime

class ProductVariantBase(BaseModel):
    variant_type: str
    variant_value: str
    price_adjustment: Decimal = Decimal('0.00')
    stock_quantity: int = 0
    sku_suffix: Optional[str] = None
    is_active: bool = True

class ProductVariant(ProductVariantBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    product_id: UUID
    created_at: datetime

class ProductBase(BaseModel):
    name: str
    slug: str
    description: Optional[str] = None
    short_description: Optional[str] = None
    base_price: Decimal
    sale_price: Optional[Decimal] = None
    currency: str = "USD"
    sku: Optional[str] = None
    stock_quantity: int = 0
    min_stock_level: int = 5
    is_active: bool = True
    is_featured: bool = False
    weight_kg: Optional[Decimal] = None
    dimensions_cm: Optional[str] = None
    material: Optional[str] = None
    care_instructions: Optional[str] = None
    tags: Optional[List[str]] = []

class ProductCreate(ProductBase):
    brand_id: Optional[UUID] = None
    category_l1_id: Optional[UUID] = None
    category_l2_id: Optional[UUID] = None
    category_l3_id: Optional[UUID] = None

class Product(ProductBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    brand_id: Optional[UUID] = None
    category_l1_id: Optional[UUID] = None
    category_l2_id: Optional[UUID] = None
    category_l3_id: Optional[UUID] = None
    rating_average: Decimal
    rating_count: int
    view_count: int
    purchase_count: int
    created_at: datetime
    updated_at: datetime
    
    # Relationships
    brand: Optional[Brand] = None
    category_l1: Optional[Category] = None
    category_l2: Optional[Category] = None
    category_l3: Optional[Category] = None
    images: List[ProductImage] = []
    variants: List[ProductVariant] = []

class ProductSummary(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    name: str
    slug: str
    short_description: Optional[str] = None
    base_price: Decimal
    sale_price: Optional[Decimal] = None
    currency: str
    rating_average: Decimal
    rating_count: int
    primary_image: Optional[str] = None
    brand_name: Optional[str] = None
    category_path: Optional[str] = None

# Chat and AI schemas
class ChatMessage(BaseModel):
    role: str = Field(..., description="Role: 'user' or 'assistant'")
    content: str = Field(..., description="Message content")
    timestamp: datetime = Field(default_factory=datetime.now)

class ChatRequest(BaseModel):
    message: str = Field(..., description="User message")
    session_token: Optional[str] = None

class ChatResponse(BaseModel):
    message: str = Field(..., description="Bot response")
    session_token: str = Field(..., description="Session token")
    suggested_products: Optional[List[ProductSummary]] = None
    suggested_categories: Optional[List[Category]] = None
    context: Optional[Dict[str, Any]] = None

class CustomerPreferences(BaseModel):
    gender: Optional[str] = None
    size_preferences: Optional[Dict[str, str]] = None
    color_preferences: Optional[List[str]] = None
    brand_preferences: Optional[List[str]] = None
    price_range: Optional[Dict[str, Decimal]] = None
    style_preferences: Optional[List[str]] = None

class SearchFilters(BaseModel):
    category_l1: Optional[str] = None
    category_l2: Optional[str] = None
    category_l3: Optional[str] = None
    brand: Optional[str] = None
    min_price: Optional[Decimal] = None
    max_price: Optional[Decimal] = None
    size: Optional[str] = None
    color: Optional[str] = None
    tags: Optional[List[str]] = None
    in_stock: bool = True

class SearchRequest(BaseModel):
    query: Optional[str] = None
    filters: Optional[SearchFilters] = None
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=20, ge=1, le=100)
    sort_by: str = Field(default="relevance")  # relevance, price_asc, price_desc, rating, newest

class SearchResponse(BaseModel):
    products: List[ProductSummary]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    filters_applied: Optional[SearchFilters] = None
