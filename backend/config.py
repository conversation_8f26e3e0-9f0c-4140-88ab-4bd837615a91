from pydantic_settings import BaseSettings
from typing import Optional
import os

class Settings(BaseSettings):
    # Database
    database_url: str = "postgresql://retail_user:retail_password@localhost:5432/retail_bot_db"
    
    # Redis
    redis_url: str = "redis://localhost:6379"
    
    # OpenAI
    openai_api_key: Optional[str] = None
    
    # API Settings
    api_title: str = "Retail Clothing Bot API"
    api_version: str = "1.0.0"
    api_description: str = "Smart retail clothing customer service bot API"
    
    # CORS
    allowed_origins: list = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    # Session
    session_expire_hours: int = 24
    
    # Pagination
    default_page_size: int = 20
    max_page_size: int = 100
    
    # Cache TTL (seconds)
    cache_ttl_categories: int = 3600  # 1 hour
    cache_ttl_products: int = 1800    # 30 minutes
    cache_ttl_search: int = 600       # 10 minutes
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# Global settings instance
settings = Settings()
