from sqlalchemy import Column, String, Integer, Decimal, Boolean, DateTime, Text, ForeignKey, ARRAY
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database import Base
import uuid

class Category(Base):
    __tablename__ = "categories"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)
    slug = Column(String(100), nullable=False, unique=True)
    level = Column(Integer, nullable=False)
    parent_id = Column(UUID(as_uuid=True), ForeignKey("categories.id"))
    description = Column(Text)
    image_url = Column(String(500))
    is_active = Column(Boolean, default=True)
    sort_order = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    parent = relationship("Category", remote_side=[id])
    children = relationship("Category", back_populates="parent")

class Brand(Base):
    __tablename__ = "brands"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False, unique=True)
    slug = Column(String(100), nullable=False, unique=True)
    description = Column(Text)
    logo_url = Column(String(500))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class Product(Base):
    __tablename__ = "products"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(200), nullable=False)
    slug = Column(String(200), nullable=False, unique=True)
    description = Column(Text)
    short_description = Column(String(500))
    brand_id = Column(UUID(as_uuid=True), ForeignKey("brands.id"))
    category_l1_id = Column(UUID(as_uuid=True), ForeignKey("categories.id"))
    category_l2_id = Column(UUID(as_uuid=True), ForeignKey("categories.id"))
    category_l3_id = Column(UUID(as_uuid=True), ForeignKey("categories.id"))
    base_price = Column(Decimal(10, 2), nullable=False)
    sale_price = Column(Decimal(10, 2))
    currency = Column(String(3), default="USD")
    sku = Column(String(100), unique=True)
    stock_quantity = Column(Integer, default=0)
    min_stock_level = Column(Integer, default=5)
    is_active = Column(Boolean, default=True)
    is_featured = Column(Boolean, default=False)
    weight_kg = Column(Decimal(5, 2))
    dimensions_cm = Column(String(50))
    material = Column(String(200))
    care_instructions = Column(Text)
    tags = Column(ARRAY(String))
    rating_average = Column(Decimal(3, 2), default=0.0)
    rating_count = Column(Integer, default=0)
    view_count = Column(Integer, default=0)
    purchase_count = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    brand = relationship("Brand")
    category_l1 = relationship("Category", foreign_keys=[category_l1_id])
    category_l2 = relationship("Category", foreign_keys=[category_l2_id])
    category_l3 = relationship("Category", foreign_keys=[category_l3_id])
    images = relationship("ProductImage", back_populates="product")
    variants = relationship("ProductVariant", back_populates="product")

class ProductImage(Base):
    __tablename__ = "product_images"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    product_id = Column(UUID(as_uuid=True), ForeignKey("products.id"))
    image_url = Column(String(500), nullable=False)
    alt_text = Column(String(200))
    is_primary = Column(Boolean, default=False)
    sort_order = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    product = relationship("Product", back_populates="images")

class ProductVariant(Base):
    __tablename__ = "product_variants"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    product_id = Column(UUID(as_uuid=True), ForeignKey("products.id"))
    variant_type = Column(String(50), nullable=False)
    variant_value = Column(String(100), nullable=False)
    price_adjustment = Column(Decimal(10, 2), default=0.00)
    stock_quantity = Column(Integer, default=0)
    sku_suffix = Column(String(50))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    product = relationship("Product", back_populates="variants")

class CustomerSession(Base):
    __tablename__ = "customer_sessions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_token = Column(String(255), unique=True, nullable=False)
    customer_preferences = Column(JSONB)
    conversation_history = Column(JSONB)
    current_context = Column(JSONB)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    expires_at = Column(DateTime(timezone=True))
