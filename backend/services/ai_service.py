from openai import AsyncOpenAI
from typing import List, Dict, Any, Optional, Tuple
import json
from datetime import datetime, timedelta
import uuid

from config import settings
from schemas import ChatMessage, CustomerPreferences, ProductSummary, Category, SearchFilters, SearchRequest
from models import Product as ProductModel, Category as CategoryModel, Brand
from sqlalchemy.orm import Session, joinedload
from routers.products import get_product_summary
from routers.search import search_products

class AIService:
    def __init__(self):
        self.client = None
        if settings.openai_api_key:
            self.client = AsyncOpenAI(api_key=settings.openai_api_key)
        
        self.system_prompt = """You are a helpful and knowledgeable retail clothing assistant. Your goal is to help customers find the perfect clothing items by asking smart questions and providing personalized recommendations.

Key Guidelines:
1. Ask 2-3 targeted questions to understand customer needs (occasion, style, budget, size, color preferences)
2. Be conversational and friendly, not robotic
3. Focus on understanding their specific needs rather than overwhelming with options
4. When you have enough information, provide specific product recommendations
5. Always consider their budget, style preferences, and practical needs
6. If they ask about specific items, provide detailed information
7. Help them navigate categories if they're browsing

Available product categories:
- Men's: Tops (T-Shirts, Dress Shirts, Polo Shirts, Tank Tops), Bottoms (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>s, Dress Pants), Outerwear, Footwear (Sneakers, Dress Shoes, Boots), Accessories
- Women's: Tops (T-Shirts, Blouses, Tank Tops, Sweaters), Bottoms (Jeans, Leggings, Skirts), Dresses, Outerwear, Footwear (Sneakers, Heels, Flats), Accessories
- Kids': Boys, Girls, Baby

Price ranges: Budget ($10-50), Mid-range ($50-150), Premium ($150+)

Always respond in a helpful, conversational tone and ask follow-up questions to better understand their needs."""

    async def generate_response(
        self, 
        user_message: str, 
        conversation_history: List[ChatMessage],
        customer_preferences: Optional[CustomerPreferences],
        db: Session
    ) -> Tuple[str, Optional[List[ProductSummary]], Optional[List[Category]], Optional[Dict[str, Any]]]:
        """Generate AI response with product recommendations"""
        
        try:
            # Build conversation context
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # Add conversation history
            for msg in conversation_history[-10:]:  # Keep last 10 messages for context
                messages.append({
                    "role": msg.role,
                    "content": msg.content
                })
            
            # Add current user message
            messages.append({"role": "user", "content": user_message})
            
            # Add customer preferences context if available
            if customer_preferences:
                pref_context = f"Customer preferences: {customer_preferences.model_dump_json()}"
                messages.append({"role": "system", "content": pref_context})
            
            # Call OpenAI API
            if not self.client:
                # Fallback if no API key
                return self._get_fallback_response(user_message, db), None, None, None

            response = await self.client.chat.completions.create(
                model="gpt-4",
                messages=messages,
                max_tokens=500,
                temperature=0.7,

                ],
                tool_choice="auto",
                tools=[
                    {
                        "type": "function",
                        "function": {
                            "name": "search_products",
                            "description": "Search for products based on customer requirements",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "query": {"type": "string", "description": "Search query"},
                                    "category_l1": {"type": "string", "description": "Level 1 category (mens, womens, kids)"},
                                    "category_l2": {"type": "string", "description": "Level 2 category"},
                                    "category_l3": {"type": "string", "description": "Level 3 category"},
                                    "brand": {"type": "string", "description": "Brand preference"},
                                    "min_price": {"type": "number", "description": "Minimum price"},
                                    "max_price": {"type": "number", "description": "Maximum price"},
                                    "size": {"type": "string", "description": "Size preference"},
                                    "color": {"type": "string", "description": "Color preference"}
                                }
                            }
                        }
                    }
                ]
            )
            
            # Process response
            ai_message = response.choices[0].message
            suggested_products = None
            suggested_categories = None
            updated_context = None

            # Handle tool calls
            if ai_message.tool_calls:
                tool_call = ai_message.tool_calls[0]
                function_name = tool_call.function.name
                function_args = json.loads(tool_call.function.arguments)
                
                if function_name == "search_products":
                    # Search for products
                    search_filters = SearchFilters(**{k: v for k, v in function_args.items() if v is not None})
                    search_request = SearchRequest(
                        query=function_args.get("query"),
                        filters=search_filters,
                        page=1,
                        page_size=6  # Limit to 6 recommendations
                    )
                    
                    # Import here to avoid circular imports
                    from routers.search import search_products as search_func
                    search_response = await search_func(search_request, db, None)
                    suggested_products = search_response.products
                
                elif function_name == "get_categories":
                    # Get category suggestions
                    level = function_args.get("level", 1)
                    parent_category = function_args.get("parent_category")
                    
                    query = db.query(CategoryModel).filter(CategoryModel.is_active == True, CategoryModel.level == level)
                    if parent_category:
                        parent = db.query(CategoryModel).filter(CategoryModel.slug == parent_category).first()
                        if parent:
                            query = query.filter(CategoryModel.parent_id == parent.id)
                    
                    categories = query.order_by(CategoryModel.sort_order, CategoryModel.name).limit(8).all()
                    suggested_categories = [Category.model_validate(cat) for cat in categories]
                
                elif function_name == "update_preferences":
                    # Update customer preferences
                    updated_context = {"updated_preferences": function_args}
                
                # Generate a follow-up response
                follow_up_messages = messages + [
                    {"role": "assistant", "content": f"I found some options for you. Let me provide recommendations."},
                    {"role": "user", "content": "Please provide your recommendations based on what you found."}
                ]
                
                follow_up_response = await self.client.chat.completions.create(
                    model="gpt-4",
                    messages=follow_up_messages,
                    max_tokens=300,
                    temperature=0.7
                )
                
                response_text = follow_up_response.choices[0].message.content
            else:
                response_text = ai_message.content or "I'm here to help you find the perfect clothing. What are you looking for today?"
            
            return response_text, suggested_products, suggested_categories, updated_context
            
        except Exception as e:
            # Fallback response if OpenAI fails
            return self._get_fallback_response(user_message, db), None, None, None
    
    def _get_fallback_response(self, user_message: str, db: Session) -> str:
        """Provide fallback response when AI service is unavailable"""
        
        # Simple keyword-based responses
        message_lower = user_message.lower()
        
        if any(word in message_lower for word in ["hello", "hi", "hey", "start"]):
            return "Hello! I'm here to help you find the perfect clothing. What are you looking for today? Are you shopping for yourself or someone else?"
        
        elif any(word in message_lower for word in ["jeans", "pants", "denim"]):
            return "Great choice! I can help you find the perfect jeans. What's your preferred fit - skinny, straight, or relaxed? And what's your size?"
        
        elif any(word in message_lower for word in ["shirt", "top", "blouse"]):
            return "I'd love to help you find a great shirt! Are you looking for something casual like a t-shirt, or more formal like a dress shirt? What's the occasion?"
        
        elif any(word in message_lower for word in ["shoes", "sneakers", "boots"]):
            return "Perfect! What type of shoes are you looking for? Athletic sneakers, dress shoes, or casual footwear? What size do you wear?"
        
        elif any(word in message_lower for word in ["dress", "formal"]):
            return "Wonderful! What's the occasion for the dress? Is it for work, a special event, or casual wear? What's your preferred style and size?"
        
        elif any(word in message_lower for word in ["budget", "price", "cost"]):
            return "I understand budget is important! What price range are you comfortable with? We have options from budget-friendly ($10-50) to premium ($150+)."
        
        else:
            return "I'd be happy to help you find what you're looking for! Could you tell me more about what type of clothing you need and what the occasion is?"
    
    async def extract_preferences(self, conversation_history: List[ChatMessage]) -> CustomerPreferences:
        """Extract customer preferences from conversation history"""
        
        preferences = CustomerPreferences()
        
        # Simple keyword extraction (could be enhanced with NLP)
        full_conversation = " ".join([msg.content.lower() for msg in conversation_history if msg.role == "user"])
        
        # Extract gender
        if any(word in full_conversation for word in ["men's", "mens", "man", "guy", "male"]):
            preferences.gender = "male"
        elif any(word in full_conversation for word in ["women's", "womens", "woman", "girl", "female", "lady"]):
            preferences.gender = "female"
        elif any(word in full_conversation for word in ["kid", "child", "boy", "girl", "baby"]):
            preferences.gender = "child"
        
        # Extract size preferences (basic)
        sizes = ["xs", "s", "m", "l", "xl", "xxl", "small", "medium", "large"]
        for size in sizes:
            if size in full_conversation:
                preferences.size_preferences = {"general": size}
                break
        
        # Extract color preferences
        colors = ["black", "white", "blue", "red", "green", "yellow", "pink", "purple", "gray", "brown"]
        found_colors = [color for color in colors if color in full_conversation]
        if found_colors:
            preferences.color_preferences = found_colors
        
        # Extract price range
        if any(word in full_conversation for word in ["budget", "cheap", "affordable", "under"]):
            preferences.price_range = {"min": 10, "max": 50}
        elif any(word in full_conversation for word in ["premium", "expensive", "high-end", "luxury"]):
            preferences.price_range = {"min": 150, "max": 500}
        else:
            preferences.price_range = {"min": 50, "max": 150}
        
        return preferences
