import ollama
import json
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import uuid
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import pickle
import os

from config import settings
from schemas import ChatMessage, CustomerPreferences, ProductSummary, Category, SearchFilters, SearchRequest
from models import Product as ProductModel, Category as CategoryModel, Brand
from sqlalchemy.orm import Session, joinedload
from routers.products import get_product_summary

class LocalAIService:
    def __init__(self):
        self.ollama_client = ollama.AsyncClient(host=settings.ollama_url)
        self.model_name = settings.local_ai_model
        
        # Learning components
        self.conversation_memory = []
        self.customer_patterns = {}
        self.product_embeddings = {}
        self.vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        
        # Load existing learning data
        self._load_learning_data()
        
        # System prompt for retail assistant
        self.system_prompt = """You are a helpful and knowledgeable retail clothing assistant. Your goal is to help customers find the perfect clothing items by asking smart questions and providing personalized recommendations.

Key Guidelines:
1. Ask 2-3 targeted questions to understand customer needs (occasion, style, budget, size, color preferences)
2. Be conversational and friendly, not robotic
3. Focus on understanding their specific needs rather than overwhelming with options
4. When you have enough information, provide specific product recommendations
5. Always consider their budget, style preferences, and practical needs
6. If they ask about specific items, provide detailed information
7. Help them navigate categories if they're browsing

Available product categories:
- Men's: Tops (T-Shirts, Dress Shirts, Polo Shirts, Tank Tops), Bottoms (Jeans, Chinos, Shorts, Dress Pants), Outerwear, Footwear (Sneakers, Dress Shoes, Boots), Accessories
- Women's: Tops (T-Shirts, Blouses, Tank Tops, Sweaters), Bottoms (Jeans, Leggings, Skirts), Dresses, Outerwear, Footwear (Sneakers, Heels, Flats), Accessories
- Kids': Boys, Girls, Baby

Price ranges: Budget ($10-50), Mid-range ($50-150), Premium ($150+)

Always respond in a helpful, conversational tone and ask follow-up questions to better understand their needs.

IMPORTANT: Keep responses concise (2-3 sentences max) and always end with a question to continue the conversation."""

    def _load_learning_data(self):
        """Load existing learning data from disk"""
        try:
            if os.path.exists('data/conversation_memory.json'):
                with open('data/conversation_memory.json', 'r') as f:
                    self.conversation_memory = json.load(f)
            
            if os.path.exists('data/customer_patterns.json'):
                with open('data/customer_patterns.json', 'r') as f:
                    self.customer_patterns = json.load(f)
            
            if os.path.exists('data/vectorizer.pkl'):
                with open('data/vectorizer.pkl', 'rb') as f:
                    self.vectorizer = pickle.load(f)
                    
        except Exception as e:
            print(f"Warning: Could not load learning data: {e}")

    def _save_learning_data(self):
        """Save learning data to disk"""
        try:
            os.makedirs('data', exist_ok=True)
            
            with open('data/conversation_memory.json', 'w') as f:
                json.dump(self.conversation_memory[-1000:], f)  # Keep last 1000 conversations
            
            with open('data/customer_patterns.json', 'w') as f:
                json.dump(self.customer_patterns, f)
            
            with open('data/vectorizer.pkl', 'wb') as f:
                pickle.dump(self.vectorizer, f)
                
        except Exception as e:
            print(f"Warning: Could not save learning data: {e}")

    async def ensure_model_available(self):
        """Ensure the AI model is downloaded and available"""
        try:
            # Check if model exists
            models = await self.ollama_client.list()
            model_names = [model['name'] for model in models['models']]
            
            if self.model_name not in model_names:
                print(f"Downloading {self.model_name} model... This may take a few minutes.")
                await self.ollama_client.pull(self.model_name)
                print(f"Model {self.model_name} downloaded successfully!")
            
            return True
        except Exception as e:
            print(f"Error ensuring model availability: {e}")
            return False

    async def generate_response(
        self, 
        user_message: str, 
        conversation_history: List[ChatMessage],
        customer_preferences: Optional[CustomerPreferences],
        db: Session
    ) -> Tuple[str, Optional[List[ProductSummary]], Optional[List[Category]], Optional[Dict[str, Any]]]:
        """Generate AI response with product recommendations and learning"""
        
        try:
            # Ensure model is available
            if not await self.ensure_model_available():
                return self._get_fallback_response(user_message, db), None, None, None
            
            # Build conversation context
            messages = []
            
            # Add conversation history (last 6 messages for context)
            for msg in conversation_history[-6:]:
                messages.append({
                    "role": msg.role,
                    "content": msg.content
                })
            
            # Add current user message
            messages.append({
                "role": "user",
                "content": user_message
            })
            
            # Add customer preferences context if available
            context_info = []
            if customer_preferences:
                if customer_preferences.gender:
                    context_info.append(f"Customer gender: {customer_preferences.gender}")
                if customer_preferences.price_range:
                    context_info.append(f"Budget: ${customer_preferences.price_range.get('min', 0)}-${customer_preferences.price_range.get('max', 1000)}")
                if customer_preferences.color_preferences:
                    context_info.append(f"Color preferences: {', '.join(customer_preferences.color_preferences)}")
            
            # Add learning context from similar conversations
            similar_context = self._get_similar_conversation_context(user_message)
            if similar_context:
                context_info.append(f"Similar customer interests: {similar_context}")
            
            # Build the prompt
            full_prompt = self.system_prompt
            if context_info:
                full_prompt += f"\n\nCustomer Context: {' | '.join(context_info)}"
            
            full_prompt += f"\n\nConversation:\n"
            for msg in messages:
                full_prompt += f"{msg['role'].title()}: {msg['content']}\n"
            
            full_prompt += "\nAssistant:"
            
            # Generate response using Ollama
            response = await self.ollama_client.generate(
                model=self.model_name,
                prompt=full_prompt,
                options={
                    'temperature': 0.7,
                    'top_p': 0.9,
                    'max_tokens': 200,
                    'stop': ['User:', 'Human:']
                }
            )
            
            ai_response = response['response'].strip()
            
            # Learn from this interaction
            self._learn_from_interaction(user_message, ai_response, customer_preferences)
            
            # Analyze if we should search for products
            suggested_products = None
            suggested_categories = None
            updated_context = None
            
            # Simple keyword-based product search triggers
            search_keywords = ['looking for', 'need', 'want', 'show me', 'find', 'buy', 'purchase']
            product_keywords = ['jeans', 'shirt', 'dress', 'shoes', 'jacket', 'pants', 'top', 'bottom']
            
            should_search = any(keyword in user_message.lower() for keyword in search_keywords)
            has_product_mention = any(keyword in user_message.lower() for keyword in product_keywords)
            
            if should_search or has_product_mention:
                suggested_products = await self._search_relevant_products(user_message, customer_preferences, db)
            
            # Extract preferences if mentioned
            extracted_prefs = self._extract_preferences_from_message(user_message)
            if extracted_prefs:
                updated_context = {"updated_preferences": extracted_prefs}
            
            return ai_response, suggested_products, suggested_categories, updated_context
            
        except Exception as e:
            print(f"Local AI error: {e}")
            # Fallback to rule-based response
            return self._get_fallback_response(user_message, db), None, None, None

    def _learn_from_interaction(self, user_message: str, ai_response: str, preferences: Optional[CustomerPreferences]):
        """Learn from customer interactions to improve future responses"""
        
        # Store conversation for learning
        interaction = {
            'timestamp': datetime.now().isoformat(),
            'user_message': user_message,
            'ai_response': ai_response,
            'preferences': preferences.model_dump() if preferences else None
        }
        
        self.conversation_memory.append(interaction)
        
        # Extract patterns
        self._extract_customer_patterns(user_message, preferences)
        
        # Save learning data periodically
        if len(self.conversation_memory) % 10 == 0:
            self._save_learning_data()

    def _extract_customer_patterns(self, message: str, preferences: Optional[CustomerPreferences]):
        """Extract and store customer behavior patterns"""
        
        message_lower = message.lower()
        
        # Extract mentioned categories
        categories = []
        if 'jeans' in message_lower: categories.append('jeans')
        if 'shirt' in message_lower: categories.append('shirts')
        if 'dress' in message_lower: categories.append('dresses')
        if 'shoes' in message_lower: categories.append('shoes')
        
        # Extract price sensitivity
        price_sensitivity = 'medium'
        if any(word in message_lower for word in ['cheap', 'budget', 'affordable']): 
            price_sensitivity = 'low'
        elif any(word in message_lower for word in ['premium', 'expensive', 'luxury']): 
            price_sensitivity = 'high'
        
        # Store patterns
        pattern_key = f"categories_{','.join(sorted(categories))}" if categories else "general"
        
        if pattern_key not in self.customer_patterns:
            self.customer_patterns[pattern_key] = {
                'frequency': 0,
                'price_sensitivity': [],
                'common_requests': []
            }
        
        self.customer_patterns[pattern_key]['frequency'] += 1
        self.customer_patterns[pattern_key]['price_sensitivity'].append(price_sensitivity)
        self.customer_patterns[pattern_key]['common_requests'].append(message_lower[:100])

    def _get_similar_conversation_context(self, current_message: str) -> str:
        """Get context from similar past conversations"""
        
        if not self.conversation_memory:
            return ""
        
        try:
            # Simple similarity based on keywords
            current_words = set(current_message.lower().split())
            
            similar_interactions = []
            for interaction in self.conversation_memory[-100:]:  # Check last 100 interactions
                past_words = set(interaction['user_message'].lower().split())
                similarity = len(current_words.intersection(past_words)) / len(current_words.union(past_words))
                
                if similarity > 0.3:  # 30% similarity threshold
                    similar_interactions.append(interaction)
            
            if similar_interactions:
                # Return common patterns from similar interactions
                common_themes = []
                for interaction in similar_interactions[-3:]:  # Last 3 similar
                    if interaction.get('preferences'):
                        prefs = interaction['preferences']
                        if prefs.get('color_preferences'):
                            common_themes.append(f"often prefers {', '.join(prefs['color_preferences'])}")
                
                return '; '.join(common_themes) if common_themes else ""
            
        except Exception as e:
            print(f"Error getting similar context: {e}")
        
        return ""

    async def _search_relevant_products(self, message: str, preferences: Optional[CustomerPreferences], db: Session) -> List[ProductSummary]:
        """Search for relevant products based on message content"""
        
        try:
            # Extract search terms
            search_terms = []
            message_lower = message.lower()
            
            # Category mapping
            if any(word in message_lower for word in ['jeans', 'denim']): search_terms.append('jeans')
            if any(word in message_lower for word in ['shirt', 'top', 'blouse']): search_terms.append('shirt')
            if any(word in message_lower for word in ['dress', 'gown']): search_terms.append('dress')
            if any(word in message_lower for word in ['shoes', 'sneakers', 'boots']): search_terms.append('shoes')
            
            # Gender detection
            gender_filter = None
            if preferences and preferences.gender:
                gender_filter = preferences.gender
            elif any(word in message_lower for word in ['men', 'mens', 'man', 'guy']): 
                gender_filter = 'male'
            elif any(word in message_lower for word in ['women', 'womens', 'woman', 'lady']): 
                gender_filter = 'female'
            
            # Build search query
            query = ' '.join(search_terms) if search_terms else message_lower
            
            # Search products
            search_query = db.query(ProductModel).options(
                joinedload(ProductModel.brand),
                joinedload(ProductModel.category_l1),
                joinedload(ProductModel.category_l2),
                joinedload(ProductModel.category_l3),
                joinedload(ProductModel.images)
            ).filter(ProductModel.is_active == True)
            
            # Apply gender filter
            if gender_filter == 'male':
                search_query = search_query.join(CategoryModel, ProductModel.category_l1_id == CategoryModel.id).filter(
                    CategoryModel.slug == 'mens'
                )
            elif gender_filter == 'female':
                search_query = search_query.join(CategoryModel, ProductModel.category_l1_id == CategoryModel.id).filter(
                    CategoryModel.slug == 'womens'
                )
            
            # Apply text search if we have search terms
            if search_terms:
                from sqlalchemy import or_, func
                search_conditions = []
                for term in search_terms:
                    search_conditions.extend([
                        ProductModel.name.ilike(f"%{term}%"),
                        ProductModel.description.ilike(f"%{term}%"),
                        ProductModel.tags.any(func.lower(func.unnest(ProductModel.tags)).like(f"%{term}%"))
                    ])
                search_query = search_query.filter(or_(*search_conditions))
            
            # Get results
            products = search_query.order_by(ProductModel.rating_average.desc()).limit(6).all()
            
            return [get_product_summary(product) for product in products]
            
        except Exception as e:
            print(f"Error searching products: {e}")
            return []

    def _extract_preferences_from_message(self, message: str) -> Dict[str, Any]:
        """Extract customer preferences from message"""
        
        preferences = {}
        message_lower = message.lower()
        
        # Extract colors
        colors = ['black', 'white', 'blue', 'red', 'green', 'yellow', 'pink', 'purple', 'gray', 'brown']
        found_colors = [color for color in colors if color in message_lower]
        if found_colors:
            preferences['color_preferences'] = found_colors
        
        # Extract size
        sizes = ['xs', 'small', 's', 'medium', 'm', 'large', 'l', 'xl', 'xxl']
        for size in sizes:
            if f' {size} ' in f' {message_lower} ' or message_lower.endswith(f' {size}'):
                preferences['size_preferences'] = {'general': size}
                break
        
        # Extract price range
        if any(word in message_lower for word in ['budget', 'cheap', 'affordable', 'under']):
            preferences['price_range'] = {'min': 10, 'max': 50}
        elif any(word in message_lower for word in ['premium', 'expensive', 'luxury']):
            preferences['price_range'] = {'min': 150, 'max': 500}
        
        return preferences

    def _get_fallback_response(self, user_message: str, db: Session) -> str:
        """Provide fallback response when AI service is unavailable"""
        
        message_lower = user_message.lower()
        
        if any(word in message_lower for word in ["hello", "hi", "hey", "start"]):
            return "Hello! I'm here to help you find the perfect clothing. What are you looking for today? Are you shopping for yourself or someone else?"
        
        elif any(word in message_lower for word in ["jeans", "pants", "denim"]):
            return "Great choice! I can help you find the perfect jeans. What's your preferred fit - skinny, straight, or relaxed? And what's your size?"
        
        elif any(word in message_lower for word in ["shirt", "top", "blouse"]):
            return "I'd love to help you find a great shirt! Are you looking for something casual like a t-shirt, or more formal like a dress shirt? What's the occasion?"
        
        elif any(word in message_lower for word in ["shoes", "sneakers", "boots"]):
            return "Perfect! What type of shoes are you looking for? Athletic sneakers, dress shoes, or casual footwear? What size do you wear?"
        
        elif any(word in message_lower for word in ["dress", "formal"]):
            return "Wonderful! What's the occasion for the dress? Is it for work, a special event, or casual wear? What's your preferred style and size?"
        
        elif any(word in message_lower for word in ["budget", "price", "cost"]):
            return "I understand budget is important! What price range are you comfortable with? We have options from budget-friendly ($10-50) to premium ($150+)."
        
        else:
            return "I'd be happy to help you find what you're looking for! Could you tell me more about what type of clothing you need and what the occasion is?"

    async def extract_preferences(self, conversation_history: List[ChatMessage]) -> CustomerPreferences:
        """Extract customer preferences from conversation history using local AI"""
        
        preferences = CustomerPreferences()
        
        # Combine all user messages
        user_messages = [msg.content for msg in conversation_history if msg.role == "user"]
        full_conversation = " ".join(user_messages).lower()
        
        # Use learned patterns to improve extraction
        preferences = self._extract_preferences_from_message(full_conversation)
        
        # Extract gender
        if any(word in full_conversation for word in ["men's", "mens", "man", "guy", "male"]):
            preferences.gender = "male"
        elif any(word in full_conversation for word in ["women's", "womens", "woman", "girl", "female", "lady"]):
            preferences.gender = "female"
        elif any(word in full_conversation for word in ["kid", "child", "boy", "girl", "baby"]):
            preferences.gender = "child"
        
        return CustomerPreferences(**preferences) if preferences else CustomerPreferences()
