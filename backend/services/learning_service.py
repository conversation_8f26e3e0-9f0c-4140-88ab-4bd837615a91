import json
import os
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans
from sklearn.metrics.pairwise import cosine_similarity
import pickle
from collections import defaultdict, Counter

from sqlalchemy.orm import Session
from models import CustomerSession, Product as ProductModel
from schemas import ChatMessage, CustomerPreferences, ProductSummary

class LearningService:
    """
    Advanced learning service that captures customer interactions,
    analyzes patterns, and improves recommendations over time.
    """
    
    def __init__(self):
        self.data_dir = "data/learning"
        os.makedirs(self.data_dir, exist_ok=True)
        
        # Learning components
        self.interaction_log = []
        self.customer_clusters = {}
        self.product_affinity_matrix = {}
        self.conversation_patterns = {}
        self.recommendation_feedback = {}
        
        # ML models
        self.text_vectorizer = TfidfVectorizer(max_features=500, stop_words='english')
        self.customer_segmentation_model = None
        
        # Load existing data
        self._load_learning_data()
    
    def _load_learning_data(self):
        """Load all learning data from disk"""
        try:
            # Load interaction log
            log_file = os.path.join(self.data_dir, "interaction_log.json")
            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    self.interaction_log = json.load(f)
            
            # Load customer clusters
            clusters_file = os.path.join(self.data_dir, "customer_clusters.json")
            if os.path.exists(clusters_file):
                with open(clusters_file, 'r') as f:
                    self.customer_clusters = json.load(f)
            
            # Load product affinity matrix
            affinity_file = os.path.join(self.data_dir, "product_affinity.json")
            if os.path.exists(affinity_file):
                with open(affinity_file, 'r') as f:
                    self.product_affinity_matrix = json.load(f)
            
            # Load conversation patterns
            patterns_file = os.path.join(self.data_dir, "conversation_patterns.json")
            if os.path.exists(patterns_file):
                with open(patterns_file, 'r') as f:
                    self.conversation_patterns = json.load(f)
            
            # Load ML models
            vectorizer_file = os.path.join(self.data_dir, "text_vectorizer.pkl")
            if os.path.exists(vectorizer_file):
                with open(vectorizer_file, 'rb') as f:
                    self.text_vectorizer = pickle.load(f)
            
            segmentation_file = os.path.join(self.data_dir, "segmentation_model.pkl")
            if os.path.exists(segmentation_file):
                with open(segmentation_file, 'rb') as f:
                    self.customer_segmentation_model = pickle.load(f)
                    
        except Exception as e:
            print(f"Warning: Could not load learning data: {e}")
    
    def _save_learning_data(self):
        """Save all learning data to disk"""
        try:
            # Save interaction log (keep last 5000 interactions)
            log_file = os.path.join(self.data_dir, "interaction_log.json")
            with open(log_file, 'w') as f:
                json.dump(self.interaction_log[-5000:], f, indent=2)
            
            # Save customer clusters
            clusters_file = os.path.join(self.data_dir, "customer_clusters.json")
            with open(clusters_file, 'w') as f:
                json.dump(self.customer_clusters, f, indent=2)
            
            # Save product affinity matrix
            affinity_file = os.path.join(self.data_dir, "product_affinity.json")
            with open(affinity_file, 'w') as f:
                json.dump(self.product_affinity_matrix, f, indent=2)
            
            # Save conversation patterns
            patterns_file = os.path.join(self.data_dir, "conversation_patterns.json")
            with open(patterns_file, 'w') as f:
                json.dump(self.conversation_patterns, f, indent=2)
            
            # Save ML models
            vectorizer_file = os.path.join(self.data_dir, "text_vectorizer.pkl")
            with open(vectorizer_file, 'wb') as f:
                pickle.dump(self.text_vectorizer, f)
            
            if self.customer_segmentation_model:
                segmentation_file = os.path.join(self.data_dir, "segmentation_model.pkl")
                with open(segmentation_file, 'wb') as f:
                    pickle.dump(self.customer_segmentation_model, f)
                    
        except Exception as e:
            print(f"Warning: Could not save learning data: {e}")
    
    def log_interaction(
        self, 
        session_token: str,
        user_message: str,
        ai_response: str,
        suggested_products: Optional[List[ProductSummary]] = None,
        customer_preferences: Optional[CustomerPreferences] = None,
        conversation_context: Optional[Dict[str, Any]] = None
    ):
        """Log a customer interaction for learning"""
        
        interaction = {
            'timestamp': datetime.now().isoformat(),
            'session_token': session_token,
            'user_message': user_message,
            'ai_response': ai_response,
            'suggested_products': [p.model_dump() for p in suggested_products] if suggested_products else [],
            'customer_preferences': customer_preferences.model_dump() if customer_preferences else None,
            'conversation_context': conversation_context or {},
            'message_length': len(user_message),
            'response_length': len(ai_response),
            'products_suggested_count': len(suggested_products) if suggested_products else 0
        }
        
        self.interaction_log.append(interaction)
        
        # Update learning models periodically
        if len(self.interaction_log) % 50 == 0:
            asyncio.create_task(self._update_learning_models())
    
    async def _update_learning_models(self):
        """Update learning models based on accumulated interactions"""
        
        try:
            # Update conversation patterns
            self._analyze_conversation_patterns()
            
            # Update customer segmentation
            self._update_customer_segmentation()
            
            # Update product affinity matrix
            self._update_product_affinity()
            
            # Save updated data
            self._save_learning_data()
            
        except Exception as e:
            print(f"Error updating learning models: {e}")
    
    def _analyze_conversation_patterns(self):
        """Analyze conversation patterns to improve responses"""
        
        # Group interactions by conversation flow
        conversation_flows = defaultdict(list)
        
        for interaction in self.interaction_log[-1000:]:  # Last 1000 interactions
            user_msg = interaction['user_message'].lower()
            ai_resp = interaction['ai_response']
            
            # Categorize user intent
            intent = self._classify_user_intent(user_msg)
            conversation_flows[intent].append({
                'user_message': user_msg,
                'ai_response': ai_resp,
                'success_indicators': self._extract_success_indicators(interaction)
            })
        
        # Analyze successful patterns for each intent
        for intent, interactions in conversation_flows.items():
            if len(interactions) >= 5:  # Minimum sample size
                successful_responses = [
                    inter['ai_response'] for inter in interactions 
                    if inter['success_indicators']['likely_successful']
                ]
                
                if successful_responses:
                    self.conversation_patterns[intent] = {
                        'sample_count': len(interactions),
                        'success_rate': len(successful_responses) / len(interactions),
                        'successful_response_patterns': self._extract_response_patterns(successful_responses),
                        'last_updated': datetime.now().isoformat()
                    }
    
    def _classify_user_intent(self, message: str) -> str:
        """Classify user intent from message"""
        
        message = message.lower()
        
        if any(word in message for word in ['hello', 'hi', 'hey', 'start']):
            return 'greeting'
        elif any(word in message for word in ['looking for', 'need', 'want', 'find']):
            return 'product_search'
        elif any(word in message for word in ['size', 'fit', 'color', 'price']):
            return 'specification_inquiry'
        elif any(word in message for word in ['recommend', 'suggest', 'advice']):
            return 'recommendation_request'
        elif any(word in message for word in ['thank', 'thanks', 'bye', 'goodbye']):
            return 'closing'
        else:
            return 'general_inquiry'
    
    def _extract_success_indicators(self, interaction: Dict[str, Any]) -> Dict[str, Any]:
        """Extract indicators of successful interaction"""
        
        user_msg = interaction['user_message'].lower()
        ai_resp = interaction['ai_response'].lower()
        
        # Positive indicators
        positive_signals = [
            'thank' in user_msg,
            'perfect' in user_msg,
            'great' in user_msg,
            'exactly' in user_msg,
            len(interaction['suggested_products']) > 0,
            interaction['products_suggested_count'] > 0
        ]
        
        # Negative indicators
        negative_signals = [
            'no' in user_msg and 'not' in user_msg,
            'wrong' in user_msg,
            'different' in user_msg,
            len(ai_resp) < 20,  # Very short responses might indicate confusion
        ]
        
        success_score = sum(positive_signals) - sum(negative_signals)
        
        return {
            'likely_successful': success_score > 0,
            'success_score': success_score,
            'positive_signals': sum(positive_signals),
            'negative_signals': sum(negative_signals)
        }
    
    def _extract_response_patterns(self, responses: List[str]) -> List[str]:
        """Extract common patterns from successful responses"""
        
        # Simple pattern extraction - look for common phrases
        common_phrases = []
        
        for response in responses:
            # Extract sentences that appear frequently
            sentences = response.split('.')
            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) > 10:  # Meaningful sentences
                    common_phrases.append(sentence)
        
        # Count phrase frequency
        phrase_counts = Counter(common_phrases)
        
        # Return most common patterns
        return [phrase for phrase, count in phrase_counts.most_common(5) if count > 1]
    
    def _update_customer_segmentation(self):
        """Update customer segmentation based on behavior patterns"""
        
        if len(self.interaction_log) < 100:  # Need minimum data
            return
        
        # Extract features for clustering
        customer_features = defaultdict(lambda: {
            'message_count': 0,
            'avg_message_length': 0,
            'product_interests': [],
            'price_sensitivity': 'medium',
            'interaction_frequency': 0
        })
        
        # Aggregate customer behavior
        for interaction in self.interaction_log[-1000:]:
            session = interaction['session_token']
            customer_features[session]['message_count'] += 1
            customer_features[session]['avg_message_length'] += interaction['message_length']
            
            # Extract product interests
            user_msg = interaction['user_message'].lower()
            for product_type in ['jeans', 'shirt', 'dress', 'shoes', 'jacket']:
                if product_type in user_msg:
                    customer_features[session]['product_interests'].append(product_type)
            
            # Extract price sensitivity
            if any(word in user_msg for word in ['budget', 'cheap', 'affordable']):
                customer_features[session]['price_sensitivity'] = 'low'
            elif any(word in user_msg for word in ['premium', 'expensive', 'luxury']):
                customer_features[session]['price_sensitivity'] = 'high'
        
        # Prepare data for clustering
        feature_vectors = []
        session_ids = []
        
        for session_id, features in customer_features.items():
            if features['message_count'] >= 2:  # Minimum interaction threshold
                # Create feature vector
                vector = [
                    features['message_count'],
                    features['avg_message_length'] / features['message_count'],
                    len(set(features['product_interests'])),
                    1 if features['price_sensitivity'] == 'low' else 2 if features['price_sensitivity'] == 'medium' else 3
                ]
                feature_vectors.append(vector)
                session_ids.append(session_id)
        
        if len(feature_vectors) >= 10:  # Minimum for clustering
            # Perform clustering
            n_clusters = min(5, len(feature_vectors) // 3)  # Dynamic cluster count
            self.customer_segmentation_model = KMeans(n_clusters=n_clusters, random_state=42)
            cluster_labels = self.customer_segmentation_model.fit_predict(feature_vectors)
            
            # Store cluster assignments
            for session_id, cluster_label in zip(session_ids, cluster_labels):
                self.customer_clusters[session_id] = {
                    'cluster_id': int(cluster_label),
                    'features': customer_features[session_id],
                    'last_updated': datetime.now().isoformat()
                }
    
    def _update_product_affinity(self):
        """Update product affinity matrix based on co-occurrence in conversations"""
        
        # Track products mentioned together
        product_co_occurrence = defaultdict(lambda: defaultdict(int))
        
        for interaction in self.interaction_log[-1000:]:
            suggested_products = interaction.get('suggested_products', [])
            
            if len(suggested_products) >= 2:
                # Count co-occurrences
                for i, product1 in enumerate(suggested_products):
                    for j, product2 in enumerate(suggested_products):
                        if i != j:
                            p1_id = product1.get('id', '')
                            p2_id = product2.get('id', '')
                            if p1_id and p2_id:
                                product_co_occurrence[p1_id][p2_id] += 1
        
        # Convert to affinity scores
        for product1, related_products in product_co_occurrence.items():
            total_occurrences = sum(related_products.values())
            if total_occurrences > 0:
                self.product_affinity_matrix[product1] = {
                    product2: count / total_occurrences 
                    for product2, count in related_products.items()
                    if count >= 2  # Minimum co-occurrence threshold
                }
    
    def get_personalized_recommendations(
        self, 
        session_token: str, 
        base_products: List[ProductSummary],
        customer_preferences: Optional[CustomerPreferences] = None
    ) -> List[ProductSummary]:
        """Get personalized product recommendations based on learning"""
        
        try:
            # Get customer cluster
            customer_cluster = self.customer_clusters.get(session_token)
            
            # Score products based on various factors
            scored_products = []
            
            for product in base_products:
                score = 1.0  # Base score
                
                # Cluster-based scoring
                if customer_cluster:
                    cluster_id = customer_cluster['cluster_id']
                    # Boost products popular in this cluster
                    cluster_boost = self._get_cluster_product_preference(cluster_id, product.id)
                    score *= (1 + cluster_boost)
                
                # Affinity-based scoring
                affinity_boost = self._get_product_affinity_score(product.id, base_products)
                score *= (1 + affinity_boost)
                
                # Preference-based scoring
                if customer_preferences:
                    preference_boost = self._get_preference_score(product, customer_preferences)
                    score *= (1 + preference_boost)
                
                scored_products.append((product, score))
            
            # Sort by score and return
            scored_products.sort(key=lambda x: x[1], reverse=True)
            return [product for product, score in scored_products]
            
        except Exception as e:
            print(f"Error in personalized recommendations: {e}")
            return base_products
    
    def _get_cluster_product_preference(self, cluster_id: int, product_id: str) -> float:
        """Get product preference score for a customer cluster"""
        
        # Count how often this product was suggested to customers in this cluster
        cluster_product_count = 0
        total_cluster_interactions = 0
        
        for interaction in self.interaction_log[-500:]:
            session_token = interaction['session_token']
            if session_token in self.customer_clusters:
                customer_cluster = self.customer_clusters[session_token]
                if customer_cluster['cluster_id'] == cluster_id:
                    total_cluster_interactions += 1
                    
                    suggested_products = interaction.get('suggested_products', [])
                    if any(p.get('id') == product_id for p in suggested_products):
                        cluster_product_count += 1
        
        if total_cluster_interactions > 0:
            return cluster_product_count / total_cluster_interactions
        return 0.0
    
    def _get_product_affinity_score(self, product_id: str, context_products: List[ProductSummary]) -> float:
        """Get affinity score based on product relationships"""
        
        if product_id not in self.product_affinity_matrix:
            return 0.0
        
        affinity_scores = []
        product_affinities = self.product_affinity_matrix[product_id]
        
        for context_product in context_products:
            context_id = context_product.id
            if context_id in product_affinities:
                affinity_scores.append(product_affinities[context_id])
        
        return np.mean(affinity_scores) if affinity_scores else 0.0
    
    def _get_preference_score(self, product: ProductSummary, preferences: CustomerPreferences) -> float:
        """Get preference-based score for a product"""
        
        score = 0.0
        
        # Price preference
        if preferences.price_range:
            min_price = preferences.price_range.get('min', 0)
            max_price = preferences.price_range.get('max', 1000)
            product_price = float(product.sale_price or product.base_price)
            
            if min_price <= product_price <= max_price:
                score += 0.3
        
        # Brand preference
        if preferences.brand_preferences and product.brand_name:
            if product.brand_name.lower() in [b.lower() for b in preferences.brand_preferences]:
                score += 0.2
        
        # Color preference (simplified - would need product color data)
        if preferences.color_preferences:
            # This would require color information in product data
            score += 0.1
        
        return score
    
    def get_learning_insights(self) -> Dict[str, Any]:
        """Get insights about the learning system performance"""
        
        total_interactions = len(self.interaction_log)
        recent_interactions = len([i for i in self.interaction_log if 
                                 datetime.fromisoformat(i['timestamp']) > datetime.now() - timedelta(days=7)])
        
        return {
            'total_interactions': total_interactions,
            'recent_interactions_7d': recent_interactions,
            'customer_clusters_count': len(self.customer_clusters),
            'conversation_patterns_count': len(self.conversation_patterns),
            'product_affinities_count': len(self.product_affinity_matrix),
            'learning_data_size_mb': self._calculate_data_size(),
            'last_model_update': max([p.get('last_updated', '') for p in self.conversation_patterns.values()] + [''])
        }
    
    def _calculate_data_size(self) -> float:
        """Calculate approximate size of learning data in MB"""
        
        try:
            total_size = 0
            for filename in os.listdir(self.data_dir):
                filepath = os.path.join(self.data_dir, filename)
                if os.path.isfile(filepath):
                    total_size += os.path.getsize(filepath)
            return round(total_size / (1024 * 1024), 2)
        except:
            return 0.0
