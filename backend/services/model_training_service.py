import json
import os
import async<PERSON>
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import ollama
from pathlib import Path

from services.learning_service import LearningService
from config import settings

class ModelTrainingService:
    """
    Service for fine-tuning and improving the local AI model based on 
    customer interactions and feedback.
    """
    
    def __init__(self):
        self.ollama_client = ollama.AsyncClient(host=settings.ollama_url)
        self.learning_service = LearningService()
        self.training_data_dir = "data/training"
        os.makedirs(self.training_data_dir, exist_ok=True)
        
        # Training configuration
        self.base_model = settings.local_ai_model
        self.custom_model_name = "retail-assistant-trained"
        self.min_training_samples = 100
        self.training_batch_size = 50
        
    async def should_retrain_model(self) -> bool:
        """Determine if the model should be retrained based on new data"""
        
        # Check if we have enough new interactions
        recent_interactions = len([
            i for i in self.learning_service.interaction_log 
            if datetime.fromisoformat(i['timestamp']) > datetime.now() - timedelta(days=7)
        ])
        
        # Check if we have enough feedback data
        feedback_count = len(self.learning_service.recommendation_feedback)
        
        # Check last training date
        last_training_file = os.path.join(self.training_data_dir, "last_training.json")
        last_training_date = None
        
        if os.path.exists(last_training_file):
            with open(last_training_file, 'r') as f:
                data = json.load(f)
                last_training_date = datetime.fromisoformat(data['timestamp'])
        
        # Retrain if:
        # 1. We have enough new interactions (>50 in last week)
        # 2. We have feedback data (>20 feedback items)
        # 3. It's been more than 7 days since last training
        should_retrain = (
            recent_interactions >= 50 or
            feedback_count >= 20 or
            (last_training_date and datetime.now() - last_training_date > timedelta(days=7))
        )
        
        return should_retrain
    
    async def prepare_training_data(self) -> List[Dict[str, str]]:
        """Prepare training data from successful interactions"""
        
        training_examples = []
        
        # Get successful interactions from learning service
        successful_interactions = []
        
        for interaction in self.learning_service.interaction_log:
            # Extract success indicators
            user_msg = interaction['user_message'].lower()
            
            # Consider interaction successful if:
            # 1. Products were suggested and user didn't express dissatisfaction
            # 2. User expressed positive sentiment
            # 3. Conversation continued naturally
            
            is_successful = (
                interaction['products_suggested_count'] > 0 and
                not any(neg_word in user_msg for neg_word in ['no', 'wrong', 'not what', 'different']) and
                (
                    any(pos_word in user_msg for pos_word in ['thanks', 'perfect', 'great', 'exactly', 'yes']) or
                    len(interaction['ai_response']) > 30  # Substantial response
                )
            )
            
            if is_successful:
                successful_interactions.append(interaction)
        
        # Convert to training format
        for interaction in successful_interactions[-500:]:  # Use last 500 successful interactions
            
            # Create training example
            training_example = {
                "instruction": "You are a helpful retail clothing assistant. Respond to the customer's message in a friendly, helpful way.",
                "input": interaction['user_message'],
                "output": interaction['ai_response']
            }
            
            training_examples.append(training_example)
        
        # Add examples from feedback data
        for feedback_key, feedback in self.learning_service.recommendation_feedback.items():
            if feedback['feedback_type'] in ['helpful', 'good_recommendation']:
                # Find the corresponding interaction
                session_token = feedback['session_token']
                
                # Look for interactions from this session around the feedback time
                feedback_time = datetime.fromisoformat(feedback['timestamp'])
                
                for interaction in self.learning_service.interaction_log:
                    if (interaction.get('session_token') == session_token and
                        abs(datetime.fromisoformat(interaction['timestamp']) - feedback_time) < timedelta(minutes=10)):
                        
                        training_example = {
                            "instruction": "You are a helpful retail clothing assistant. This response received positive feedback.",
                            "input": interaction['user_message'],
                            "output": interaction['ai_response']
                        }
                        
                        training_examples.append(training_example)
                        break
        
        return training_examples
    
    async def create_fine_tuned_model(self, training_data: List[Dict[str, str]]) -> bool:
        """Create a fine-tuned model using the training data"""
        
        try:
            # Save training data
            training_file = os.path.join(self.training_data_dir, f"training_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            with open(training_file, 'w') as f:
                json.dump(training_data, f, indent=2)
            
            # Create model file for fine-tuning
            modelfile_content = await self._create_enhanced_modelfile(training_data)
            
            modelfile_path = os.path.join(self.training_data_dir, "enhanced_modelfile")
            with open(modelfile_path, 'w') as f:
                f.write(modelfile_content)
            
            # Create the enhanced model
            print(f"Creating enhanced model: {self.custom_model_name}")
            
            # Use Ollama to create the model
            result = await self.ollama_client.create(
                model=self.custom_model_name,
                modelfile=modelfile_content
            )
            
            # Record training completion
            training_record = {
                'timestamp': datetime.now().isoformat(),
                'training_samples': len(training_data),
                'base_model': self.base_model,
                'custom_model': self.custom_model_name,
                'training_file': training_file
            }
            
            last_training_file = os.path.join(self.training_data_dir, "last_training.json")
            with open(last_training_file, 'w') as f:
                json.dump(training_record, f, indent=2)
            
            print(f"✅ Model training completed successfully!")
            print(f"   - Training samples: {len(training_data)}")
            print(f"   - Model name: {self.custom_model_name}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error during model training: {e}")
            return False
    
    async def _create_enhanced_modelfile(self, training_data: List[Dict[str, str]]) -> str:
        """Create an enhanced modelfile with training insights"""
        
        # Analyze training data to extract patterns
        common_intents = self._analyze_training_intents(training_data)
        successful_patterns = self._extract_successful_patterns(training_data)
        
        modelfile = f"""FROM {self.base_model}

# Enhanced parameters based on training data
PARAMETER temperature 0.7
PARAMETER top_p 0.9
PARAMETER top_k 40
PARAMETER repeat_penalty 1.1
PARAMETER num_ctx 2048

# Enhanced system prompt with learned patterns
SYSTEM \"\"\"You are an expert retail clothing assistant with extensive experience helping customers find perfect clothing items. You have learned from thousands of successful customer interactions.

CORE PRINCIPLES:
1. Ask 2-3 targeted questions to understand customer needs (occasion, style, budget, size, color preferences)
2. Be conversational and friendly, never robotic
3. Focus on understanding specific needs rather than overwhelming with options
4. Provide specific product recommendations when you have enough information
5. Always consider budget, style preferences, and practical needs
6. Provide detailed information when asked about specific items
7. Help navigate categories when customers are browsing

LEARNED SUCCESSFUL PATTERNS:
{self._format_successful_patterns(successful_patterns)}

COMMON CUSTOMER INTENTS:
{self._format_common_intents(common_intents)}

PRODUCT CATEGORIES:
- Men's: Tops (T-Shirts, Dress Shirts, Polo Shirts, Tank Tops), Bottoms (Jeans, Chinos, Shorts, Dress Pants), Outerwear, Footwear (Sneakers, Dress Shoes, Boots), Accessories
- Women's: Tops (T-Shirts, Blouses, Tank Tops, Sweaters), Bottoms (Jeans, Leggings, Skirts), Dresses, Outerwear, Footwear (Sneakers, Heels, Flats), Accessories
- Kids': Boys, Girls, Baby

PRICE RANGES: Budget ($10-50), Mid-range ($50-150), Premium ($150+)

RESPONSE GUIDELINES:
- Keep responses concise (2-3 sentences max)
- Always end with a question to continue the conversation
- Use friendly, conversational tone
- Show enthusiasm for helping customers
- Acknowledge their preferences and build on them

EXAMPLE SUCCESSFUL INTERACTIONS:
{self._format_training_examples(training_data[:5])}

Remember: Your goal is to help customers find exactly what they're looking for through smart questions and personalized recommendations.
\"\"\"
"""
        
        return modelfile
    
    def _analyze_training_intents(self, training_data: List[Dict[str, str]]) -> Dict[str, int]:
        """Analyze common customer intents from training data"""
        
        intents = {
            'product_search': 0,
            'size_inquiry': 0,
            'price_inquiry': 0,
            'style_advice': 0,
            'occasion_based': 0,
            'brand_inquiry': 0,
            'greeting': 0
        }
        
        for example in training_data:
            input_text = example['input'].lower()
            
            if any(word in input_text for word in ['looking for', 'need', 'want', 'find']):
                intents['product_search'] += 1
            if any(word in input_text for word in ['size', 'fit', 'large', 'small', 'medium']):
                intents['size_inquiry'] += 1
            if any(word in input_text for word in ['price', 'cost', 'budget', 'cheap', 'expensive']):
                intents['price_inquiry'] += 1
            if any(word in input_text for word in ['style', 'look', 'fashion', 'trendy']):
                intents['style_advice'] += 1
            if any(word in input_text for word in ['work', 'party', 'wedding', 'casual', 'formal']):
                intents['occasion_based'] += 1
            if any(word in input_text for word in ['brand', 'nike', 'adidas', 'zara', 'h&m']):
                intents['brand_inquiry'] += 1
            if any(word in input_text for word in ['hello', 'hi', 'hey']):
                intents['greeting'] += 1
        
        return intents
    
    def _extract_successful_patterns(self, training_data: List[Dict[str, str]]) -> List[str]:
        """Extract patterns from successful responses"""
        
        patterns = []
        
        # Analyze response structures
        question_starters = []
        helpful_phrases = []
        
        for example in training_data:
            output = example['output']
            
            # Extract questions
            if '?' in output:
                sentences = output.split('.')
                for sentence in sentences:
                    if '?' in sentence:
                        question_starters.append(sentence.strip().split()[0:3])
            
            # Extract helpful phrases
            if any(word in output.lower() for word in ['great', 'perfect', 'excellent']):
                helpful_phrases.append(output[:50] + "...")
        
        # Get most common patterns
        from collections import Counter
        
        common_question_starters = Counter([' '.join(q) for q in question_starters]).most_common(3)
        patterns.extend([f"Question pattern: {pattern}" for pattern, count in common_question_starters])
        
        return patterns[:5]  # Top 5 patterns
    
    def _format_successful_patterns(self, patterns: List[str]) -> str:
        """Format successful patterns for the modelfile"""
        if not patterns:
            return "- Ask specific questions about customer needs\n- Show enthusiasm and helpfulness"
        
        return '\n'.join([f"- {pattern}" for pattern in patterns])
    
    def _format_common_intents(self, intents: Dict[str, int]) -> str:
        """Format common intents for the modelfile"""
        
        sorted_intents = sorted(intents.items(), key=lambda x: x[1], reverse=True)
        
        intent_descriptions = {
            'product_search': 'Customers looking for specific products',
            'size_inquiry': 'Questions about sizing and fit',
            'price_inquiry': 'Budget and pricing questions',
            'style_advice': 'Style and fashion advice requests',
            'occasion_based': 'Shopping for specific occasions',
            'brand_inquiry': 'Brand-specific questions',
            'greeting': 'Initial greetings and conversation starters'
        }
        
        formatted = []
        for intent, count in sorted_intents[:5]:
            if count > 0:
                formatted.append(f"- {intent_descriptions.get(intent, intent)}: {count} examples")
        
        return '\n'.join(formatted)
    
    def _format_training_examples(self, examples: List[Dict[str, str]]) -> str:
        """Format training examples for the modelfile"""
        
        formatted = []
        for i, example in enumerate(examples, 1):
            formatted.append(f"Example {i}:")
            formatted.append(f"Customer: {example['input']}")
            formatted.append(f"Assistant: {example['output']}")
            formatted.append("")
        
        return '\n'.join(formatted)
    
    async def run_training_pipeline(self) -> Dict[str, Any]:
        """Run the complete training pipeline"""
        
        result = {
            'training_started': datetime.now().isoformat(),
            'should_retrain': False,
            'training_completed': False,
            'training_samples': 0,
            'error': None
        }
        
        try:
            # Check if retraining is needed
            should_retrain = await self.should_retrain_model()
            result['should_retrain'] = should_retrain
            
            if not should_retrain:
                result['message'] = "No retraining needed at this time"
                return result
            
            print("🚀 Starting model training pipeline...")
            
            # Prepare training data
            training_data = await self.prepare_training_data()
            result['training_samples'] = len(training_data)
            
            if len(training_data) < self.min_training_samples:
                result['error'] = f"Insufficient training data: {len(training_data)} < {self.min_training_samples}"
                return result
            
            # Create fine-tuned model
            training_success = await self.create_fine_tuned_model(training_data)
            result['training_completed'] = training_success
            
            if training_success:
                result['message'] = f"Model training completed successfully with {len(training_data)} samples"
                result['model_name'] = self.custom_model_name
            else:
                result['error'] = "Model training failed"
            
        except Exception as e:
            result['error'] = str(e)
        
        result['training_finished'] = datetime.now().isoformat()
        return result
    
    async def get_training_status(self) -> Dict[str, Any]:
        """Get current training status and statistics"""
        
        # Check if custom model exists
        try:
            models = await self.ollama_client.list()
            model_names = [model['name'] for model in models['models']]
            custom_model_exists = self.custom_model_name in model_names
        except:
            custom_model_exists = False
        
        # Get last training info
        last_training_file = os.path.join(self.training_data_dir, "last_training.json")
        last_training_info = None
        
        if os.path.exists(last_training_file):
            with open(last_training_file, 'r') as f:
                last_training_info = json.load(f)
        
        # Get available training data
        available_interactions = len(self.learning_service.interaction_log)
        recent_interactions = len([
            i for i in self.learning_service.interaction_log 
            if datetime.fromisoformat(i['timestamp']) > datetime.now() - timedelta(days=7)
        ])
        
        return {
            'custom_model_exists': custom_model_exists,
            'custom_model_name': self.custom_model_name,
            'last_training': last_training_info,
            'available_interactions': available_interactions,
            'recent_interactions_7d': recent_interactions,
            'min_training_samples': self.min_training_samples,
            'ready_for_training': available_interactions >= self.min_training_samples,
            'should_retrain': await self.should_retrain_model()
        }
