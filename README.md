# Retail Clothing Bot - Smart Shopping Assistant

A production-ready, AI-powered retail clothing customer service bot that helps customers find the perfect products through intelligent conversations and personalized recommendations.

## 🚀 Features

- **Smart AI Conversations**: GPT-4 powered chat bot that understands customer needs
- **3-Level Category Hierarchy**: Organized product catalog (L1 → L2 → L3)
- **Intelligent Product Search**: Advanced filtering and recommendation engine
- **Real-time Chat Interface**: Modern React-based chat UI with WebSocket support
- **Scalable Architecture**: Microservices with <PERSON><PERSON>, <PERSON>is caching, and PostgreSQL
- **Production Ready**: Comprehensive error handling, logging, and monitoring

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Chat    │    │   FastAPI       │    │   PostgreSQL    │
│   Interface     │◄──►│   Backend       │◄──►│   Database      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   OpenAI GPT    │    │   Redis Cache   │
                       │   Integration   │    │                 │
                       └─────────────────┘    └─────────────────┘
```

## 🛠️ Technology Stack

### Backend
- **FastAPI**: High-performance Python web framework
- **PostgreSQL**: Primary database with JSONB support
- **Redis**: Caching and session management
- **OpenAI GPT-4**: AI conversation engine
- **SQLAlchemy**: ORM with async support

### Frontend
- **React 18**: Modern UI framework
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first styling
- **Framer Motion**: Smooth animations
- **Zustand**: State management

### Infrastructure
- **Docker**: Containerization
- **Docker Compose**: Multi-service orchestration
- **Nginx**: Reverse proxy (production)

## 📊 Database Schema

### Category Hierarchy (3 Levels)
- **L1**: Men's, Women's, Kids'
- **L2**: Tops, Bottoms, Outerwear, Footwear, Accessories
- **L3**: T-Shirts, Jeans, Sneakers, Watches, etc.

### Sample Data
- **50+ Products** across all categories
- **10 Brands** (Nike, Adidas, Zara, H&M, etc.)
- **Product Variants** (sizes, colors)
- **Realistic Pricing** and ratings

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- OpenAI API key (for AI features)

### 1. Clone and Setup
```bash
git clone <repository-url>
cd Service_Request_Bot

# Copy environment file
cp .env.example .env

# Add your OpenAI API key to .env
OPENAI_API_KEY=your_api_key_here
```

### 2. Start Services
```bash
# Start all services
docker-compose up -d

# Check service health
docker-compose ps
```

### 3. Initialize Database
```bash
# The database will be automatically initialized with schema and seed data
# Check logs to ensure successful startup
docker-compose logs postgres
```

### 4. Access the Application
- **Frontend**: http://localhost:3000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## 🔧 Development

### Backend Development
```bash
cd backend

# Install dependencies
pip install -r requirements.txt

# Run development server
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend Development
```bash
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev
```

### Database Management
```bash
# Connect to PostgreSQL
docker-compose exec postgres psql -U retail_user -d retail_bot_db

# View tables
\dt

# Check sample data
SELECT COUNT(*) FROM products;
SELECT COUNT(*) FROM categories;
```

## 🤖 AI Chat Features

### Smart Questioning
- Understands customer intent
- Asks relevant follow-up questions
- Learns from conversation context

### Product Recommendations
- Personalized suggestions based on preferences
- Price range filtering
- Style and occasion matching

### Category Navigation
- Intelligent category suggestions
- Hierarchical browsing
- Quick category shortcuts

## 📱 API Endpoints

### Chat
- `POST /api/v1/chat/` - Send message to AI bot
- `GET /api/v1/chat/session/{token}` - Get session info
- `DELETE /api/v1/chat/session/{token}` - Clear session

### Products
- `GET /api/v1/products/` - List products with filters
- `GET /api/v1/products/{id}` - Get product details
- `GET /api/v1/products/featured/` - Get featured products

### Search
- `POST /api/v1/search/` - Advanced product search
- `GET /api/v1/search/` - Simple search with query params

### Categories
- `GET /api/v1/categories/` - List categories
- `GET /api/v1/categories/tree` - Get category hierarchy
- `GET /api/v1/categories/l1` - Get L1 categories

## 🔒 Security Features

- Input validation and sanitization
- SQL injection prevention
- Rate limiting (production)
- CORS configuration
- Session management

## 📈 Performance Optimizations

- Redis caching for frequent queries
- Database indexing for fast searches
- Image optimization and CDN ready
- Lazy loading and pagination
- Connection pooling

## 🧪 Testing

```bash
# Backend tests
cd backend
pytest

# Frontend tests
cd frontend
npm test

# Integration tests
docker-compose -f docker-compose.test.yml up
```

## 🚀 Production Deployment

### Environment Variables
```bash
# Production settings
DATABASE_URL=***********************************/retail_bot
REDIS_URL=redis://prod-redis:6379
OPENAI_API_KEY=prod_api_key
```

### Docker Production Build
```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Deploy
docker-compose -f docker-compose.prod.yml up -d
```

## 📊 Monitoring

- Health check endpoints
- Application logs
- Database performance metrics
- Redis cache hit rates
- API response times

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the API documentation at `/docs`
- Review the logs: `docker-compose logs`
- Open an issue on GitHub

---

**Built with ❤️ for modern retail experiences**
